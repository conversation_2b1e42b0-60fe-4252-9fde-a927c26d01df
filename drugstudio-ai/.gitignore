# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# 日志
logs/*
!logs/.gitkeep
*.log

# 环境变量和配置
.env.local
.env.*.local

# 数据库
*.db
*.sqlite3

# 临时文件
.pytest_cache/
.coverage
htmlcov/
.tox/

# 模型和大文件
*.model
*.pkl
*.h5


models/