#!/usr/bin/env python3
"""
删除数据库中的模块相关表的脚本
"""
import sys
import os
from sqlalchemy import text, inspect

# 添加项目根路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import SessionLocal


def get_sync_db():
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        return db
    except:
        db.close()
        raise


def check_table_exists(db, table_name: str) -> bool:
    """检查表是否存在"""
    inspector = inspect(db.bind)
    return table_name in inspector.get_table_names()


def drop_table(db, table_name: str) -> bool:
    """删除指定表"""
    try:
        if check_table_exists(db, table_name):
            db.execute(text(f"DROP TABLE IF EXISTS {table_name} CASCADE"))
            db.commit()
            print(f"✅ 表 '{table_name}' 删除成功")
            return True
        else:
            print(f"⚠️  表 '{table_name}' 不存在")
            return False
    except Exception as e:
        print(f"❌ 删除表 '{table_name}' 失败: {e}")
        db.rollback()
        return False


def list_module_tables(db):
    """列出所有模块相关的表"""
    inspector = inspect(db.bind)
    all_tables = inspector.get_table_names()

    # 查找模块相关的表
    module_tables = []
    for table in all_tables:
        if "module" in table.lower():
            module_tables.append(table)

    return module_tables


def main():
    """主函数"""
    print("🗑️  数据库模块表删除工具")
    print("=" * 50)

    # 获取数据库连接
    db = get_sync_db()

    try:
        # 列出所有模块相关的表
        module_tables = list_module_tables(db)

        if not module_tables:
            print("✅ 没有找到模块相关的表")
            return

        print("发现以下模块相关的表:")
        for i, table in enumerate(module_tables, 1):
            print(f"  {i}. {table}")

        print("\n选择操作:")
        print("  1. 删除所有模块表")
        print("  2. 选择特定的表删除")
        print("  3. 仅删除 'modules' 表 (如果存在)")
        print("  4. 仅删除 'ai_modules' 表 (如果存在)")
        print("  0. 退出")

        choice = input("\n请输入选择 (0-4): ").strip()

        if choice == "0":
            print("🚪 退出程序")
            return

        elif choice == "1":
            # 删除所有模块表
            confirm = (
                input(
                    f"\n⚠️  确定要删除所有 {len(module_tables)} 个模块表吗? (yes/no): "
                )
                .strip()
                .lower()
            )
            if confirm == "yes":
                deleted_count = 0
                for table in module_tables:
                    if drop_table(db, table):
                        deleted_count += 1
                print(f"\n✅ 总共删除了 {deleted_count} 个表")
            else:
                print("❌ 操作已取消")

        elif choice == "2":
            # 选择特定表删除
            print("\n选择要删除的表 (输入表编号，用逗号分隔，如: 1,3):")
            table_nums = input("表编号: ").strip()

            try:
                selected_indices = [int(x.strip()) - 1 for x in table_nums.split(",")]
                selected_tables = [
                    module_tables[i]
                    for i in selected_indices
                    if 0 <= i < len(module_tables)
                ]

                if selected_tables:
                    print(f"\n将要删除的表: {', '.join(selected_tables)}")
                    confirm = input("确定删除吗? (yes/no): ").strip().lower()
                    if confirm == "yes":
                        deleted_count = 0
                        for table in selected_tables:
                            if drop_table(db, table):
                                deleted_count += 1
                        print(f"\n✅ 总共删除了 {deleted_count} 个表")
                    else:
                        print("❌ 操作已取消")
                else:
                    print("❌ 没有选择有效的表")
            except (ValueError, IndexError):
                print("❌ 输入格式错误")

        elif choice == "3":
            # 仅删除 modules 表
            confirm = (
                input("\n⚠️  确定要删除 'modules' 表吗? (yes/no): ").strip().lower()
            )
            if confirm == "yes":
                drop_table(db, "modules")
            else:
                print("❌ 操作已取消")

        elif choice == "4":
            # 仅删除 ai_modules 表
            confirm = (
                input("\n⚠️  确定要删除 'ai_modules' 表吗? (yes/no): ").strip().lower()
            )
            if confirm == "yes":
                drop_table(db, "ai_modules")
            else:
                print("❌ 操作已取消")

        else:
            print("❌ 无效的选择")

    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        db.rollback()

    finally:
        db.close()


if __name__ == "__main__":
    main()
