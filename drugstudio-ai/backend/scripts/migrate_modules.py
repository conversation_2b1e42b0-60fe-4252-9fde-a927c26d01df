import asyncio
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))

from app.db.database import AsyncSessionLocal
from app.db.init_modules import init_all_modules_data


async def migrate_modules():
    """迁移模块数据"""
    print("开始迁移模块数据...")

    async with AsyncSessionLocal() as session:
        try:
            await init_all_modules_data(session)
            print("模块数据迁移成功！")
        except Exception as e:
            print(f"模块数据迁移失败: {e}")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(migrate_modules())
