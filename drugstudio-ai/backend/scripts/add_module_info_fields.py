#!/usr/bin/env python3
"""
添加模块信息表格所需的新字段
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.database import AsyncSessionLocal


async def add_module_info_fields():
    """添加模块信息表格所需的新字段"""
    print("开始添加模块信息字段...")
    
    async with AsyncSessionLocal() as session:
        try:
            # 添加UUID字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS uuid UUID DEFAULT gen_random_uuid() UNIQUE
            """))
            
            # 添加封面图片字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS cover_image VARCHAR(500)
            """))
            
            # 添加特性字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS features JSON DEFAULT '[]'::json
            """))
            
            # 添加作者字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS author VARCHAR(200)
            """))
            
            # 添加发布时间字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """))
            
            # 添加参考文献字段
            await session.execute(text("""
                ALTER TABLE modules 
                ADD COLUMN IF NOT EXISTS references JSON DEFAULT '[]'::json
            """))
            
            # 为现有记录生成UUID（如果没有的话）
            await session.execute(text("""
                UPDATE modules 
                SET uuid = gen_random_uuid() 
                WHERE uuid IS NULL
            """))
            
            # 提交更改
            await session.commit()
            print("✅ 模块信息字段添加成功！")
            
        except Exception as e:
            await session.rollback()
            print(f"❌ 添加模块信息字段失败: {e}")
            sys.exit(1)


async def main():
    """主函数"""
    print("🔧 模块信息字段迁移工具")
    print("=" * 50)
    
    try:
        await add_module_info_fields()
        print("\n🎉 迁移完成！")
    except Exception as e:
        print(f"\n💥 迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
