#!/usr/bin/env python3
"""
修改数据库中的markdown_path字段，去掉前面的molecule_generation等前缀
"""
import sys
import os
import asyncio
from pathlib import Path

# 添加项目根路径到sys.path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.db.database import AsyncSessionLocal


async def update_markdown_paths():
    """更新markdown_path字段，去掉前缀"""

    async with AsyncSessionLocal() as session:
        try:
            # 查询当前的markdown_path值
            print("正在查询当前的markdown_path值...")
            result = await session.execute(
                text(
                    "SELECT id, module_id, name, markdown_path FROM modules WHERE markdown_path IS NOT NULL"
                )
            )
            modules = result.fetchall()

            if not modules:
                print("没有找到需要更新的模块")
                return

            print(f"找到 {len(modules)} 个模块需要更新:")
            for module in modules:
                print(
                    f"  - {module.module_id}: {module.name} -> {module.markdown_path}"
                )

            # 确认是否继续
            confirm = input("\n是否继续更新这些路径? (y/N): ").strip().lower()
            if confirm != "y":
                print("操作已取消")
                return

            # 更新markdown_path字段
            updated_count = 0
            for module in modules:
                old_path = module.markdown_path

                # 提取文件名部分（去掉目录前缀）
                if "/" in old_path:
                    new_path = old_path.split("/")[-1]  # 取最后一部分
                else:
                    new_path = old_path  # 如果没有斜杠，保持原样

                # 只更新确实有变化的记录
                if new_path != old_path:
                    await session.execute(
                        text(
                            "UPDATE modules SET markdown_path = :new_path WHERE id = :module_id"
                        ),
                        {"new_path": new_path, "module_id": module.id},
                    )
                    print(f"✓ 更新 {module.module_id}: {old_path} -> {new_path}")
                    updated_count += 1
                else:
                    print(f"- 跳过 {module.module_id}: {old_path} (无需更新)")

            # 提交更改
            await session.commit()
            print(f"\n✅ 成功更新了 {updated_count} 个模块的markdown_path字段")

            # 验证更新结果
            print("\n验证更新结果:")
            result = await session.execute(
                text(
                    "SELECT module_id, name, markdown_path FROM modules WHERE markdown_path IS NOT NULL ORDER BY module_id"
                )
            )
            updated_modules = result.fetchall()

            for module in updated_modules:
                print(
                    f"  - {module.module_id}: {module.name} -> {module.markdown_path}"
                )

        except Exception as e:
            await session.rollback()
            print(f"❌ 更新失败: {str(e)}")
            raise


async def main():
    """主函数"""
    print("=== 数据库 markdown_path 字段更新脚本 ===")
    print("此脚本将去掉 markdown_path 字段中的目录前缀，只保留文件名")
    print("例如: molecule_generation/001.md -> 001.md")
    print()

    try:
        await update_markdown_paths()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"脚本执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
