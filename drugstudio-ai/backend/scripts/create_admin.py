#!/usr/bin/env python
# backend/scripts/create_admin.py
import asyncio
import sys
import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Add the project root directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.user_service import UserService
from app.models.base import Base
from app.models.user import UserRole
from app.config.settings import settings

async def create_admin_user():
    # Create async engine
    engine = create_async_engine(settings.DATABASE_URL)
    
    # Create async session
    async_session = sessionmaker(
        engine, expire_on_commit=False, class_=AsyncSession
    )
    
    async with engine.begin() as conn:
        # Create tables if they don't exist
        await conn.run_sync(Base.metadata.create_all)
    
    async with async_session() as session:
        user_service = UserService(session)
        
        # Create admin user data
        admin_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "123456",
            "full_name": "Admin User",
            "is_superuser": True
        }
        
        try:
            # Check if admin role exists
            role_result = await session.execute(
                "SELECT id FROM user_roles WHERE name = 'admin'"
            )
            admin_role = role_result.first()
            
            if not admin_role:
                # Create admin role
                admin_role = UserRole(
                    name="admin",
                    description="Administrator",
                    permissions=["read:all", "write:all", "delete:all", "admin:all"]
                )
                session.add(admin_role)
                await session.commit()
                await session.refresh(admin_role)
                admin_data["role_id"] = admin_role.id
            else:
                admin_data["role_id"] = admin_role.id
                
            # Create admin user
            try:
                user = await user_service.create_admin_user(admin_data)
                print(f"Admin user created successfully with ID: {user.id}")
            except ValueError as e:
                print(f"Could not create admin user: {str(e)}")
                print("If admin user already exists, you can ignore this message.")
                
        except Exception as e:
            print(f"Error creating admin user: {str(e)}")
    
    # Close engine
    await engine.dispose()

if __name__ == "__main__":
    asyncio.run(create_admin_user())
