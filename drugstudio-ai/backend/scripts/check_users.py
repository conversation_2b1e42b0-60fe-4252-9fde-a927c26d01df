#!/usr/bin/env python3
"""
查看数据库中的用户信息
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import get_async_db
from app.models.user import User, UserRole
from app.services.user_service import UserService
from sqlalchemy import select
from sqlalchemy.orm import joinedload


async def check_users():
    """查看所有用户"""
    async for db in get_async_db():
        try:
            # 查询所有用户，包含角色信息
            result = await db.execute(select(User).options(joinedload(User.role)))
            users = result.scalars().all()

            print(f"\n📊 数据库中共有 {len(users)} 个用户:")
            print("=" * 80)

            if not users:
                print("❌ 暂无用户数据")
                return

            for i, user in enumerate(users, 1):
                print(f"\n👤 用户 {i}:")
                print(f"   ID: {user.id}")
                print(f"   用户名: {user.username}")
                print(f"   邮箱: {user.email or '未设置'}")
                print(f"   手机号: {user.phone or '未设置'}")
                print(f"   全名: {user.full_name or '未设置'}")
                print(f"   角色: {user.role.name if user.role else '无角色'}")
                print(f"   状态: {'✅ 激活' if user.is_active else '❌ 未激活'}")
                print(f"   验证: {'✅ 已验证' if user.is_verified else '❌ 未验证'}")
                print(f"   超级用户: {'✅ 是' if user.is_superuser else '❌ 否'}")
                print(f"   创建时间: {user.created_at}")
                print(f"   最后登录: {user.last_login or '从未登录'}")
                print(f"   登录次数: {user.login_count}")
                print("-" * 50)

        except Exception as e:
            print(f"❌ 查询用户时出错: {e}")
        finally:
            break


async def check_user_by_username(username: str):
    """根据用户名查询特定用户"""
    async for db in get_async_db():
        try:
            user_service = UserService(db)
            user = await user_service.get_user_by_username(username)

            if user:
                print(f"\n✅ 找到用户: {username}")
                print("=" * 50)
                print(f"ID: {user.id}")
                print(f"用户名: {user.username}")
                print(f"邮箱: {user.email or '未设置'}")
                print(f"手机号: {user.phone or '未设置'}")
                print(f"全名: {user.full_name or '未设置'}")
                print(f"状态: {'✅ 激活' if user.is_active else '❌ 未激活'}")
                print(f"验证: {'✅ 已验证' if user.is_verified else '❌ 未验证'}")
                print(f"超级用户: {'✅ 是' if user.is_superuser else '❌ 否'}")
                print(f"创建时间: {user.created_at}")
                print(f"最后登录: {user.last_login or '从未登录'}")
            else:
                print(f"❌ 未找到用户: {username}")

        except Exception as e:
            print(f"❌ 查询用户时出错: {e}")
        finally:
            break


async def check_user_stats():
    """查看用户统计信息"""
    async for db in get_async_db():
        try:
            # 总用户数
            result = await db.execute(select(User))
            total_users = len(result.scalars().all())

            # 激活用户数
            result = await db.execute(select(User).where(User.is_active == True))
            active_users = len(result.scalars().all())

            # 已验证用户数
            result = await db.execute(select(User).where(User.is_verified == True))
            verified_users = len(result.scalars().all())

            # 超级用户数
            result = await db.execute(select(User).where(User.is_superuser == True))
            super_users = len(result.scalars().all())

            print("\n📈 用户统计:")
            print("=" * 30)
            print(f"总用户数: {total_users}")
            print(f"激活用户: {active_users}")
            print(f"已验证用户: {verified_users}")
            print(f"超级用户: {super_users}")
            print(f"未激活用户: {total_users - active_users}")
            print(f"未验证用户: {total_users - verified_users}")

        except Exception as e:
            print(f"❌ 查询统计信息时出错: {e}")
        finally:
            break


async def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == "stats":
            await check_user_stats()
        elif command == "find" and len(sys.argv) > 2:
            username = sys.argv[2]
            await check_user_by_username(username)
        else:
            print("用法:")
            print("  python check_users.py           # 查看所有用户")
            print("  python check_users.py stats     # 查看用户统计")
            print("  python check_users.py find <用户名>  # 查找特定用户")
    else:
        await check_users()


if __name__ == "__main__":
    asyncio.run(main())
