#!/usr/bin/env python
# backend/scripts/create_admin_user.py
import os
import sys
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

# Add the project root directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.database import get_async_db
from app.models.user import User, UserRole
from app.core.security import get_password_hash

async def create_admin_user():
    """
    Create an admin superuser with username 'admin' and password '123456'
    """
    # Get async DB session
    db_gen = get_async_db()
    db = await anext(db_gen)
    try:
        # Check if admin user already exists
        result = await db.execute(select(User).where(User.username == "admin"))
        admin_user = result.scalars().first()
        
        if admin_user:
            print("Admin user already exists.")
            return
        
        # Check if admin role exists
        role_result = await db.execute(select(UserRole).where(UserRole.name == "admin"))
        admin_role = role_result.scalars().first()
        
        # Create admin role if it doesn't exist
        if not admin_role:
            admin_role = UserRole(
                name="admin",
                description="Administrator",
                permissions=["read:all", "write:all", "delete:all", "admin:all"]
            )
            db.add(admin_role)
            await db.flush()
            role_id = admin_role.id
        else:
            role_id = admin_role.id
        
        # Create the admin user
        hashed_password = get_password_hash("123456")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            phone=None,  # Phone is optional now
            hashed_password=hashed_password,
            full_name="Admin User",
            role_id=role_id,
            is_active=True,
            is_superuser=True,
            is_verified=True
        )
        
        db.add(admin_user)
        await db.commit()
        print("Admin user created successfully with username 'admin' and password '123456'")
        
    except Exception as e:
        print(f"Error creating admin user: {str(e)}")
        await db.rollback()
        raise

if __name__ == "__main__":
    asyncio.run(create_admin_user())
