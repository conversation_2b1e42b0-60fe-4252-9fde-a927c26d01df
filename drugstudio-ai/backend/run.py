# backend/run.py
#!/usr/bin/env python3
"""
DrugStudio AI Platform 启动脚本
"""
import uvicorn
import click
from app.config.settings import settings


@click.command()
@click.option("--host", default=settings.HOST, help="Host to bind")
@click.option("--port", default=settings.PORT, help="Port to bind")
@click.option(
    "--reload", is_flag=True, default=settings.DEBUG, help="Enable auto-reload"
)
@click.option("--workers", default=1, help="Number of worker processes")
def main(host: str, port: int, reload: bool, workers: int):
    """启动DrugStudio AI Platform"""

    if reload and workers > 1:
        click.echo(
            "Warning: --reload and --workers > 1 are mutually exclusive. Using --reload."
        )
        workers = 1

    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=reload,
        workers=workers,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        use_colors=True,
    )


if __name__ == "__main__":
    main()
