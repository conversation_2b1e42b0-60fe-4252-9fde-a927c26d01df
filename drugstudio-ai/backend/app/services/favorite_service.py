from typing import List, Optional, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload
from app.models.favorite import ModuleFavorite
from app.models.module import Module
from app.models.user import User


class FavoriteService:
    """收藏服务类"""

    @staticmethod
    async def add_favorite(
        db: AsyncSession, user_id: int, module_id: int
    ) -> ModuleFavorite:
        """添加收藏"""
        # 检查是否已经收藏
        existing = await FavoriteService.get_favorite(db, user_id, module_id)
        if existing:
            print(f"模块 {module_id} 已被用户 {user_id} 收藏")
            return existing

        # 创建新收藏
        favorite = ModuleFavorite(user_id=user_id, module_id=module_id)
        db.add(favorite)
        await db.commit()
        await db.refresh(favorite)
        print(f"用户 {user_id} 成功收藏模块 {module_id}")
        return favorite

    @staticmethod
    async def remove_favorite(db: AsyncSession, user_id: int, module_id: int) -> bool:
        """取消收藏"""
        favorite = await FavoriteService.get_favorite(db, user_id, module_id)
        if not favorite:
            print(f"用户 {user_id} 未收藏模块 {module_id}")
            return False

        await db.delete(favorite)
        await db.commit()
        print(f"用户 {user_id} 成功取消收藏模块 {module_id}")
        return True

    @staticmethod
    async def get_favorite(
        db: AsyncSession, user_id: int, module_id: int
    ) -> Optional[ModuleFavorite]:
        """获取单个收藏记录"""
        query = select(ModuleFavorite).where(
            and_(
                ModuleFavorite.user_id == user_id, ModuleFavorite.module_id == module_id
            )
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def is_favorited(db: AsyncSession, user_id: int, module_id: int) -> bool:
        """检查是否已收藏"""
        favorite = await FavoriteService.get_favorite(db, user_id, module_id)
        return favorite is not None

    @staticmethod
    async def get_user_favorites(
        db: AsyncSession, user_id: int, skip: int = 0, limit: int = 100
    ) -> tuple[List[Module], int]:
        """获取用户收藏的模块列表"""
        # 查询用户收藏的模块
        query = (
            select(Module)
            .join(ModuleFavorite)
            .options(
                selectinload(Module.parameters),
                selectinload(Module.models),
                selectinload(Module.results),
            )
            .where(ModuleFavorite.user_id == user_id)
            .order_by(ModuleFavorite.created_at.desc())
        )

        # 获取总数
        count_query = (
            select(func.count(Module.id))
            .join(ModuleFavorite)
            .where(ModuleFavorite.user_id == user_id)
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        result = await db.execute(query.offset(skip).limit(limit))
        modules = result.scalars().all()

        return modules, total

    @staticmethod
    async def get_user_favorite_module_ids(db: AsyncSession, user_id: int) -> List[int]:
        """获取用户收藏的模块ID列表"""
        query = select(ModuleFavorite.module_id).where(
            ModuleFavorite.user_id == user_id
        )
        result = await db.execute(query)
        return [row[0] for row in result.fetchall()]

    @staticmethod
    async def get_module_favorite_count(db: AsyncSession, module_id: int) -> int:
        """获取模块的收藏数量"""
        query = select(func.count(ModuleFavorite.id)).where(
            ModuleFavorite.module_id == module_id
        )
        result = await db.execute(query)
        return result.scalar() or 0

    @staticmethod
    async def batch_check_favorites(
        db: AsyncSession, user_id: int, module_ids: List[int]
    ) -> Dict[int, bool]:
        """批量检查收藏状态"""
        query = select(ModuleFavorite.module_id).where(
            and_(
                ModuleFavorite.user_id == user_id,
                ModuleFavorite.module_id.in_(module_ids),
            )
        )
        result = await db.execute(query)
        favorited_ids = {row[0] for row in result.fetchall()}

        return {module_id: module_id in favorited_ids for module_id in module_ids}
