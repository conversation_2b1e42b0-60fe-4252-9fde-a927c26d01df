# backend/app/services/user_service.py
from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_
from sqlalchemy.orm import joinedload

from app.models.user import User, UserRole, UserSession, VerificationToken
from app.core.security import verify_password, get_password_hash
from app.schemas.user import UserCreate, UserResponse


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """通过ID获取用户"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalars().first()

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        result = await self.db.execute(
            select(User).where(User.username == username)
        )
        return result.scalars().first()

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """通过邮箱获取用户"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalars().first()

    async def get_user_by_phone(self, phone: str) -> Optional[User]:
        """通过手机号获取用户"""
        result = await self.db.execute(
            select(User).where(User.phone == phone)
        )
        return result.scalars().first()

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        # 支持使用用户名、邮箱或手机号登录
        result = await self.db.execute(
            select(User).where(
                or_(
                    User.username == username,
                    User.email == username,
                    User.phone == username
                )
            )
        )
        user = result.scalars().first()
        
        if not user:
            return None
            
        if not verify_password(password, user.hashed_password):
            return None
            
        return user

    async def create_user(self, user_data: UserCreate) -> User:
        """创建新用户"""
        # 检查用户名是否存在
        if await self.get_user_by_username(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否存在
        if user_data.email and await self.get_user_by_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 检查手机号是否存在
        if user_data.phone and await self.get_user_by_phone(user_data.phone):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已被注册"
            )
        
        # 获取默认角色
        default_role_result = await self.db.execute(
            select(UserRole).where(UserRole.name == "user")
        )
        default_role = default_role_result.scalars().first()
        
        # 如果没有默认角色，创建一个
        if not default_role:
            default_role = UserRole(
                name="user",
                description="普通用户",
                permissions=["read:own"]
            )
            self.db.add(default_role)
            await self.db.flush()
            
        # 创建新用户
        hashed_password = get_password_hash(user_data.password)
        user = User(
            username=user_data.username,
            email=user_data.email,
            phone=user_data.phone,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            role_id=default_role.id,
            is_active=True,
            is_verified=False
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
        
    async def create_admin_user(self, user_data: Dict[str, Any]) -> User:
        """管理员创建用户"""
        # 检查用户名是否存在
        if await self.get_user_by_username(user_data["username"]):
            raise ValueError("用户名已存在")
        
        # 检查邮箱是否存在
        if "email" in user_data and user_data["email"] and await self.get_user_by_email(user_data["email"]):
            raise ValueError("邮箱已被注册")
        
        # 检查手机号是否存在
        if "phone" in user_data and user_data["phone"] and await self.get_user_by_phone(user_data["phone"]):
            raise ValueError("手机号已被注册")
        
        # 创建新用户
        hashed_password = get_password_hash(user_data["password"])
        
        user = User(
            username=user_data["username"],
            email=user_data.get("email"),
            phone=user_data.get("phone"),
            hashed_password=hashed_password,
            full_name=user_data.get("full_name"),
            role_id=user_data.get("role_id"),
            is_active=True,
            is_superuser=user_data.get("is_superuser", False),
            is_verified=True
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user

    async def update_user(self, user_id: int, user_data: Dict[str, Any]) -> User:
        """更新用户信息"""
        user = await self.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 更新可以修改的字段
        for field in ["full_name", "avatar_url", "bio"]:
            if field in user_data and user_data[field] is not None:
                setattr(user, field, user_data[field])
        
        # 邮箱更新需要验证
        if "email" in user_data and user_data["email"] != user.email:
            existing_email = await self.get_user_by_email(user_data["email"])
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被注册"
                )
            user.email = user_data["email"]
            user.is_verified = False
        
        # 手机号更新需要验证
        if "phone" in user_data and user_data["phone"] != user.phone:
            existing_phone = await self.get_user_by_phone(user_data["phone"])
            if existing_phone:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="手机号已被注册"
                )
            user.phone = user_data["phone"]
        
        user.updated_at = datetime.utcnow()
        await self.db.commit()
        await self.db.refresh(user)
        
        return user

    async def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """修改用户密码"""
        user = await self.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        if not verify_password(old_password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码不正确"
            )
        
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        return True

    async def reset_password(self, phone: str, new_password: str) -> bool:
        """通过手机号重置用户密码（忘记密码）"""
        user = await self.get_user_by_phone(phone)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        return True
        
    async def reset_password_by_email(self, email: str, new_password: str) -> bool:
        """通过邮箱重置用户密码（忘记密码）"""
        user = await self.get_user_by_email(email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        return True

    async def create_user_session(self, user_id: int, user_agent: str = None, ip_address: str = None) -> UserSession:
        """创建用户会话"""
        session = UserSession.create_session(
            user_id=user_id,
            user_agent=user_agent,
            ip_address=ip_address
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        
        # 更新用户最后登录时间
        user = await self.get_user_by_id(user_id)
        user.last_login = datetime.utcnow()
        user.login_count += 1
        await self.db.commit()
        
        return session

    async def invalidate_session(self, session_token: str) -> bool:
        """使会话失效（登出）"""
        result = await self.db.execute(
            select(UserSession).where(UserSession.session_token == session_token)
        )
        session = result.scalars().first()
        
        if not session:
            return False
        
        session.is_valid = False
        await self.db.commit()
        
        return True

    async def get_user_permissions(self, user_id: int) -> List[str]:
        """获取用户权限"""
        result = await self.db.execute(
            select(User).options(joinedload(User.role)).where(User.id == user_id)
        )
        user = result.scalars().first()
        
        if not user or not user.role:
            return []
        
        return user.role.permissions

    async def create_verification_token(self, user_id: int, token_type: str, expires_minutes: int = 30) -> VerificationToken:
        """创建验证令牌"""
        token = VerificationToken.create_token(
            user_id=user_id,
            token_type=token_type,
            expires_minutes=expires_minutes
        )
        
        self.db.add(token)
        await self.db.commit()
        await self.db.refresh(token)
        
        return token

    async def verify_token(self, token: str, token_type: str) -> Optional[User]:
        """验证令牌有效性并返回关联用户"""
        result = await self.db.execute(
            select(VerificationToken).where(
                and_(
                    VerificationToken.token == token,
                    VerificationToken.token_type == token_type,
                    VerificationToken.is_used == False
                )
            )
        )
        verification_token = result.scalars().first()
        
        if not verification_token or not verification_token.is_valid():
            return None
        
        # 标记令牌为已使用
        verification_token.is_used = True
        await self.db.commit()
        
        # 获取关联用户
        user = await self.get_user_by_id(verification_token.user_id)
        return user

    async def update_user_role(self, user_id: int, role_id: int) -> User:
        """更新用户角色"""
        user = await self.get_user_by_id(user_id)
        
        if not user:
            raise ValueError("用户不存在")
        
        # 检查角色是否存在
        result = await self.db.execute(
            select(UserRole).where(UserRole.id == role_id)
        )
        role = result.scalars().first()
        
        if not role:
            raise ValueError("角色不存在")
        
        user.role_id = role_id
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
        
    async def user_to_response(self, user: User) -> UserResponse:
        """将用户模型转换为响应模型"""
        # 获取用户角色
        role_name = None
        permissions = []
        
        if user.role_id:
            result = await self.db.execute(
                select(UserRole).where(UserRole.id == user.role_id)
            )
            role = result.scalars().first()
            if role:
                role_name = role.name
                permissions = role.permissions
        
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            full_name=user.full_name,
            avatar_url=user.avatar_url,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            role=role_name,
            permissions=permissions
        )
