from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from app.models.notice import Notice
from app.schemas.notice import NoticeCreate, NoticeUpdate


def get_notice(db: Session, notice_id: int) -> Optional[Notice]:
    return db.query(Notice).filter(Notice.id == notice_id).first()


def get_notices(
    db: Session, skip: int = 0, limit: int = 100
) -> Dict[str, Any]:
    notices = db.query(Notice).order_by(Notice.created_at.desc()).offset(skip).limit(limit).all()
    # 检查数据库中是否有通知，如果没有则创建样例通知（仅用于演示）
    if db.query(Notice).count() == 0:
        create_sample_notices(db)
    total = db.query(Notice).count()
    return {
        "items": notices,
        "total": total
    }


def create_notice(db: Session, obj_in: NoticeCreate) -> Notice:
    db_obj = Notice(
        title=obj_in.title,
        content=obj_in.content,
        type=obj_in.type,
        is_read=False
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_notice(
    db: Session, *, db_obj: Notice, obj_in: NoticeUpdate
) -> Notice:
    obj_data = obj_in.dict(exclude_unset=True)
    for key, value in obj_data.items():
        setattr(db_obj, key, value)
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def mark_as_read(db: Session, *, notice_id: int) -> Optional[Notice]:
    db_obj = get_notice(db, notice_id)
    if not db_obj:
        return None
    db_obj.is_read = True
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_notice(db: Session, *, notice_id: int) -> Optional[Notice]:
    db_obj = get_notice(db, notice_id)
    if not db_obj:
        return None
    db.delete(db_obj)
    db.commit()
    return db_obj


# Function to create initial sample notices for testing
def create_sample_notices(db: Session) -> List[Notice]:
    samples = [
        {
            "title": "系统通知", 
            "content": "欢迎使用DrugStudio AI平台，这是一条系统通知。",
            "type": "info"
        },
        {
            "title": "更新提醒", 
            "content": "系统将于今晚22:00进行例行维护，请提前保存您的工作。",
            "type": "warning"
        },
        {
            "title": "操作成功", 
            "content": "您的分子设计已成功提交到计算队列。",
            "type": "success"
        },
        {
            "title": "错误提醒", 
            "content": "您的上一次计算任务由于资源不足而失败，请稍后重试。",
            "type": "error"
        }
    ]
    
    result = []
    for sample in samples:
        notice = NoticeCreate(**sample)
        result.append(create_notice(db, notice))
    
    return result
