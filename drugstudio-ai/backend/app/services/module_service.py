from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from app.models.module import (
    Module,
    ModuleParameter,
    ModuleModel,
    ModuleResult,
    ModuleCategory,
    ModuleCategoryEnum,
    ModuleStatusEnum,
)
from app.schemas.module import ModuleCreate, ModuleUpdate


class ModuleService:
    """模块服务类"""

    @staticmethod
    async def get_modules(
        db: AsyncSession,
        category: Optional[ModuleCategoryEnum] = None,
        status: Optional[ModuleStatusEnum] = ModuleStatusEnum.ACTIVE,
        is_featured: Optional[bool] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[List[Module], int]:
        """获取模块列表"""

        # 构建查询条件
        conditions = []
        if category:
            conditions.append(Module.category == category)
        if status:
            conditions.append(Module.status == status)
        if is_featured is not None:
            conditions.append(Module.is_featured == is_featured)
        if search:
            search_condition = or_(
                Module.name.ilike(f"%{search}%"),
                Module.short_description.ilike(f"%{search}%"),
                Module.description.ilike(f"%{search}%"),
            )
            conditions.append(search_condition)

        # 查询模块列表
        query = (
            select(Module)
            .options(
                selectinload(Module.parameters),
                selectinload(Module.models),
                selectinload(Module.results),
            )
            .where(and_(*conditions))
            .order_by(Module.sort_order, Module.created_at.desc())
        )

        # 获取总数
        count_query = select(func.count(Module.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        result = await db.execute(query.offset(skip).limit(limit))
        modules = result.scalars().all()

        return modules, total

    @staticmethod
    async def get_module_by_id(db: AsyncSession, module_id: int) -> Optional[Module]:
        """根据ID获取模块"""
        query = (
            select(Module)
            .options(
                selectinload(Module.parameters),
                selectinload(Module.models),
                selectinload(Module.results),
            )
            .where(Module.id == module_id)
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_module_by_module_id(
        db: AsyncSession, module_id: str
    ) -> Optional[Module]:
        """根据module_id获取模块"""
        query = (
            select(Module)
            .options(
                selectinload(Module.parameters),
                selectinload(Module.models),
                selectinload(Module.results),
            )
            .where(Module.module_id == module_id)
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def create_module(
        db: AsyncSession, module_data: ModuleCreate, user_id: int
    ) -> Module:
        """创建模块"""
        # 创建主模块
        module = Module(
            module_id=module_data.module_id,
            name=module_data.name,
            short_description=module_data.short_description,
            description=module_data.description,
            markdown_path=module_data.markdown_path,
            category=module_data.category,
            tags=module_data.tags,
            icon=module_data.icon,
            color=module_data.color,
            status=module_data.status,
            sort_order=module_data.sort_order,
            is_featured=module_data.is_featured,
            version=module_data.version,
            framework=module_data.framework,
            algorithm=module_data.algorithm,
            route_path=module_data.route_path,
            api_endpoint=module_data.api_endpoint,
            documentation_url=module_data.documentation_url,
            molecule_count_desc=module_data.molecule_count_desc,
            seed_desc=module_data.seed_desc,
            created_by=user_id,
            updated_by=user_id,
        )

        db.add(module)
        await db.flush()  # 获取模块ID

        # 创建参数
        if module_data.parameters:
            for idx, param_data in enumerate(module_data.parameters):
                parameter = ModuleParameter(
                    module_id=module.id,
                    name=param_data.name,
                    description=param_data.description,
                    parameter_type=getattr(param_data, "parameter_type", "string"),
                    default_value=getattr(param_data, "default_value", None),
                    is_required=getattr(param_data, "is_required", False),
                    sort_order=idx,
                )
                db.add(parameter)

        # 创建模型
        if module_data.models:
            for idx, model_data in enumerate(module_data.models):
                model = ModuleModel(
                    module_id=module.id,
                    name=model_data.name,
                    description=model_data.description,
                    model_type=getattr(model_data, "model_type", None),
                    model_path=getattr(model_data, "model_path", None),
                    is_default=getattr(model_data, "is_default", False),
                    sort_order=idx,
                )
                db.add(model)

        # 创建结果配置
        if module_data.results:
            for idx, result_data in enumerate(module_data.results):
                result = ModuleResult(
                    module_id=module.id,
                    file_name=result_data.file_name,
                    description=result_data.description,
                    file_type=getattr(result_data, "file_type", None),
                    sort_order=idx,
                )
                db.add(result)

        await db.commit()
        await db.refresh(module)
        return module

    @staticmethod
    async def update_module(
        db: AsyncSession, module_id: int, module_data: ModuleUpdate, user_id: int
    ) -> Optional[Module]:
        """更新模块"""
        module = await ModuleService.get_module_by_id(db, module_id)
        if not module:
            return None

        # 更新基础信息
        update_data = module_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(module, field) and field not in [
                "parameters",
                "models",
                "results",
            ]:
                setattr(module, field, value)

        module.updated_by = user_id

        await db.commit()
        await db.refresh(module)
        return module

    @staticmethod
    async def delete_module(db: AsyncSession, module_id: int) -> bool:
        """删除模块"""
        module = await ModuleService.get_module_by_id(db, module_id)
        if not module:
            return False

        await db.delete(module)
        await db.commit()
        return True

    @staticmethod
    async def get_categories(db: AsyncSession) -> List[ModuleCategory]:
        """获取所有类目"""
        query = (
            select(ModuleCategory)
            .where(ModuleCategory.is_active == True)
            .order_by(ModuleCategory.sort_order)
        )

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def increase_popularity(db: AsyncSession, module_id: int) -> Optional[Module]:
        """增加模块受欢迎程度"""
        module = await ModuleService.get_module_by_id(db, module_id)
        if not module:
            return None

        module.popularity += 1
        await db.commit()
        await db.refresh(module)
        return module
