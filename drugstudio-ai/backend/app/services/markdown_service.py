import os
import aiofiles
from pathlib import Path
from typing import Optional
from fastapi import HTTPException
from cachetools import T<PERSON><PERSON>ache
import hashlib


class MarkdownService:
    """Markdown文件处理服务"""

    # 定义markdown文件的根目录
    MARKDOWN_ROOT = Path(__file__).parent.parent.parent / "docs" / "modules"

    # 内存缓存，缓存5分钟
    _cache = TTLCache(maxsize=100, ttl=300)

    @classmethod
    async def read_markdown_file(cls, file_path: str) -> str:
        """读取markdown文件内容（带缓存）"""
        cache_key = f"md_{hashlib.md5(file_path.encode()).hexdigest()}"

        # 检查缓存
        if cache_key in cls._cache:
            return cls._cache[cache_key]

        # 读取文件
        content = await cls._read_file_content(file_path)

        # 缓存内容
        cls._cache[cache_key] = content
        return content

    @classmethod
    async def _read_file_content(cls, file_path: str) -> str:
        """读取markdown文件内容"""
        try:
            # 安全检查：防止路径遍历攻击
            full_path = cls.MARKDOWN_ROOT / file_path
            full_path = full_path.resolve()

            print(full_path)
            # 确保文件在允许的目录内
            if not str(full_path).startswith(str(cls.MARKDOWN_ROOT.resolve())):
                raise HTTPException(status_code=400, detail="无效的文件路径")

            # 检查文件是否存在
            if not full_path.exists():
                raise HTTPException(status_code=404, detail="Markdown文件不存在")

            # 异步读取文件
            async with aiofiles.open(full_path, "r", encoding="utf-8") as file:
                content = await file.read()
                return content

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")

    @classmethod
    def validate_markdown_path(cls, file_path: str) -> bool:
        """验证markdown文件路径是否有效"""
        try:
            full_path = cls.MARKDOWN_ROOT / file_path
            full_path = full_path.resolve()

            # 路径安全检查
            if not str(full_path).startswith(str(cls.MARKDOWN_ROOT.resolve())):
                return False

            # 文件存在检查
            return full_path.exists() and full_path.is_file()
        except:
            return False
