from typing import Any, Dict, List
from app.db.multi_database import DatabaseManager


class DataService:
    """数据服务 - 根据数据类型选择合适的存储"""

    async def store_molecule_data(self, molecule_data: Dict[str, Any]):
        """存储分子数据到MongoDB"""
        mongodb = await get_mongodb()
        collection = mongodb.molecules
        return await collection.insert_one(molecule_data)

    async def cache_computation_result(self, key: str, result: Any, ttl: int = 3600):
        """缓存计算结果到Redis"""
        redis_client = await get_redis()
        return await redis_client.setex(key, ttl, result)

    async def store_user_session(self, session_data: Dict[str, Any]):
        """存储用户会话到Redis"""
        redis_client = await get_redis()
        return await redis_client.hset("sessions", session_data["id"], session_data)

    async def log_api_access(self, access_data: Dict[str, Any]):
        """记录API访问日志到PostgreSQL"""
        # 这里使用PostgreSQL存储结构化的审计数据
        pass
