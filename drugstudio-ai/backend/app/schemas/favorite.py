from typing import List
from datetime import datetime
from pydantic import BaseModel, Field
from app.schemas.module import ModuleResponse


class FavoriteRequest(BaseModel):
    """收藏请求Schema"""

    module_id: str = Field(..., description="模块ID")


class FavoriteResponse(BaseModel):
    """收藏响应Schema"""

    id: int
    user_id: int
    module_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class FavoriteToggleResponse(BaseModel):
    """收藏切换响应Schema"""

    is_favorited: bool = Field(..., description="是否已收藏")
    message: str = Field(..., description="操作结果消息")


class UserFavoritesResponse(BaseModel):
    """用户收藏列表响应Schema"""

    data: List[ModuleResponse]
    total: int
    page: int = 1
    size: int = 100


class FavoriteStatusResponse(BaseModel):
    """收藏状态响应Schema"""

    module_id: str
    is_favorited: bool
    favorite_count: int = Field(0, description="收藏总数")
