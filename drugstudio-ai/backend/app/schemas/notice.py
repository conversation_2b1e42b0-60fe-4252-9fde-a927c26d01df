from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


class NoticeBase(BaseModel):
    title: str
    content: str
    type: str  # info, success, warning, error


class NoticeCreate(NoticeBase):
    pass


class NoticeUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    type: Optional[str] = None
    is_read: Optional[bool] = None


class NoticeInDBBase(NoticeBase):
    id: int
    is_read: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class Notice(NoticeInDBBase):
    pass


class NoticeList(BaseModel):
    items: List[Notice]
    total: int
