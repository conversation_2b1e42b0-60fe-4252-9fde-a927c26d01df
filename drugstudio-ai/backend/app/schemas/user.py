# backend/app/schemas/user.py
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator
import re


class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

    @validator("phone")
    def validate_phone(cls, v):
        if v and not re.match(r"^\d{11}$", v):
            raise ValueError("手机号必须是11位数字")
        return v


class UserCreate(UserBase):
    password: str = Field(..., min_length=6)

    @validator("password")
    def password_validate(cls, v):
        if len(v) < 6:
            raise ValueError("密码不能少于6位")
        return v


class UserLogin(BaseModel):
    username: str
    password: str
    verificationCode: Optional[str] = None


class UserPasswordChange(BaseModel):
    old_password: str
    new_password: str = Field(..., min_length=6)

    @validator("new_password")
    def password_validate(cls, v):
        if len(v) < 6:
            raise ValueError("密码不能少于6位")
        return v


class UserPasswordReset(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=6)
    password2: str

    @validator("password2")
    def passwords_match(cls, v, values):
        if "password" in values and v != values["password"]:
            raise ValueError("两次输入的密码不一致")
        return v


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class TokenResponse(BaseModel):
    """包装后的令牌响应"""

    data: Token


class TokenData(BaseModel):
    username: Optional[str] = None


class UserInDB(UserBase):
    id: int
    is_active: bool
    is_superuser: bool
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True


class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_superuser: bool
    role: Optional[str] = None
    permissions: List[str] = []

    class Config:
        from_attributes = True
