from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import uuid
from app.models.module import ModuleCategoryEnum, ModuleStatusEnum


class ModuleCategory(str, Enum):
    """模块类目枚举"""

    MOLECULE_GENERATION = "molecule_generation"  # 分子生成
    PROTEIN_DESIGN = "protein_design"  # 蛋白设计
    ANTIBODY_DESIGN = "antibody_design"  # 抗体设计
    PEPTIDE_DESIGN = "peptide_design"  # 多肽设计
    NUCLEUS_DESIGN = "nucleus_design"  # 核酸设计
    TARGET_RECOGNITION = "target_recognition"  # 靶点识别
    VIRTUAL_SCREENING = "virtual_screening"  # 虚拟筛选
    PROPERTY_PREDICTION = "property_prediction"  # 性质预测
    MOLECULE_OPTIMIZATION = "molecule_optimization"  # 分子优化
    MOLECULE_DOCKING = "molecule_docking"  # 分子对接
    DATABASE_COMPOUND = "database_compound"  # 数据合库


class ModuleParameterBase(BaseModel):
    """模块参数基础Schema"""

    name: str = Field(..., description="参数名称")
    description: str = Field(..., description="参数描述")
    parameter_type: str = Field("string", description="参数类型")
    default_value: Optional[str] = Field(None, description="默认值")
    is_required: bool = Field(False, description="是否必需")


class ModuleParameterCreate(ModuleParameterBase):
    pass


class ModuleParameterResponse(ModuleParameterBase):
    id: int
    sort_order: int

    class Config:
        from_attributes = True


class ModuleModelBase(BaseModel):
    """模块模型基础Schema"""

    name: str = Field(..., description="模型名称")
    description: str = Field(..., description="模型描述")
    model_type: Optional[str] = Field(None, description="模型类型")
    model_path: Optional[str] = Field(None, description="模型文件路径")
    is_default: bool = Field(False, description="是否为默认模型")


class ModuleModelCreate(ModuleModelBase):
    pass


class ModuleModelResponse(ModuleModelBase):
    id: int
    sort_order: int

    class Config:
        from_attributes = True


class ModuleResultBase(BaseModel):
    """模块结果基础Schema"""

    file_name: str = Field(..., description="结果文件名")
    description: str = Field(..., description="结果描述")
    file_type: Optional[str] = Field(None, description="文件类型")


class ModuleResultCreate(ModuleResultBase):
    pass


class ModuleResultResponse(ModuleResultBase):
    id: int
    sort_order: int

    class Config:
        from_attributes = True


class ModuleBase(BaseModel):
    """模块基础Schema"""

    module_id: str = Field(..., description="模块唯一标识")
    name: str = Field(..., description="模块名称")
    short_description: str = Field(..., description="简短描述")
    description: str = Field(..., description="详细描述")
    markdown_path: str = Field(..., description="Markdown文件路径")

    # 展示信息
    cover_image: Optional[str] = Field(None, description="封面图片URL")
    features: List[str] = Field(default_factory=list, description="特性列表")
    author: Optional[str] = Field(None, description="作者")
    references: List[dict] = Field(default_factory=list, description="参考文献列表")

    category: ModuleCategoryEnum = Field(..., description="模块类目")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    icon: Optional[str] = Field(None, description="图标名称")
    color: Optional[str] = Field(None, description="主题色")
    status: ModuleStatusEnum = Field(ModuleStatusEnum.ACTIVE, description="模块状态")
    sort_order: int = Field(0, description="排序顺序")
    is_featured: bool = Field(False, description="是否为特色模块")
    version: str = Field("1.0.0", description="版本号")
    framework: Optional[str] = Field(None, description="使用的框架")
    algorithm: Optional[str] = Field(None, description="算法名称")
    route_path: Optional[str] = Field(None, description="前端路由路径")
    api_endpoint: Optional[str] = Field(None, description="API端点")
    documentation_url: Optional[str] = Field(None, description="文档链接")
    molecule_count_desc: str = Field("期望的分子数目", description="分子数量描述")
    seed_desc: str = Field("随机种子", description="随机种子描述")


class ModuleCreate(ModuleBase):
    """创建模块Schema"""

    parameters: Optional[List[ModuleParameterCreate]] = Field(default_factory=list)
    models: Optional[List[ModuleModelCreate]] = Field(default_factory=list)
    results: Optional[List[ModuleResultCreate]] = Field(default_factory=list)


class ModuleUpdate(BaseModel):
    """更新模块Schema"""

    name: Optional[str] = Field(None, description="模块名称")
    short_description: Optional[str] = Field(None, description="简短描述")
    description: Optional[str] = Field(None, description="详细描述")
    markdown_path: Optional[str] = Field(None, description="Markdown文件路径")

    # 展示信息
    cover_image: Optional[str] = Field(None, description="封面图片URL")
    features: Optional[List[str]] = Field(None, description="特性列表")
    author: Optional[str] = Field(None, description="作者")
    references: Optional[List[dict]] = Field(None, description="参考文献列表")

    category: Optional[ModuleCategoryEnum] = Field(None, description="模块类目")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    icon: Optional[str] = Field(None, description="图标名称")
    color: Optional[str] = Field(None, description="主题色")
    status: Optional[ModuleStatusEnum] = Field(None, description="模块状态")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_featured: Optional[bool] = Field(None, description="是否为特色模块")
    version: Optional[str] = Field(None, description="版本号")
    framework: Optional[str] = Field(None, description="使用的框架")
    algorithm: Optional[str] = Field(None, description="算法名称")
    route_path: Optional[str] = Field(None, description="前端路由路径")
    api_endpoint: Optional[str] = Field(None, description="API端点")
    documentation_url: Optional[str] = Field(None, description="文档链接")
    molecule_count_desc: Optional[str] = Field(None, description="分子数量描述")
    seed_desc: Optional[str] = Field(None, description="随机种子描述")


class ModuleResponse(BaseModel):
    """模块响应Schema - 匹配前端格式"""

    id: str = Field(..., description="模块ID")  # 前端期望的格式
    uuid: str = Field(..., description="模块UUID")
    name: str
    shortDescription: str
    description: str
    markdownContent: str

    # 展示信息
    coverImage: Optional[str] = Field(None, description="封面图片URL")
    features: List[str] = Field(default_factory=list, description="特性列表")
    author: Optional[str] = Field(None, description="作者")
    publishDate: Optional[datetime] = Field(None, description="发布时间")
    references: List[dict] = Field(default_factory=list, description="参考文献列表")

    category: ModuleCategoryEnum
    tags: List[str]
    icon: Optional[str]
    parameters: List[dict] = Field(default_factory=list)
    models: List[dict] = Field(default_factory=list)
    moleculeCount: str
    seed: str
    results: List[dict] = Field(default_factory=list)
    is_favorited: Optional[bool] = Field(False, description="是否已收藏")

    class Config:
        from_attributes = True


class ModuleListResponse(BaseModel):
    """模块列表响应Schema"""

    data: List[ModuleResponse]
    total: int
    category: Optional[ModuleCategoryEnum] = None


class CategoryInfoBase(BaseModel):
    """类目信息基础Schema"""

    key: str = Field(..., description="类目键")
    name: str = Field(..., description="类目名称")
    description: Optional[str] = Field(None, description="类目描述")
    icon: Optional[str] = Field(None, description="类目图标")
    color: Optional[str] = Field(None, description="主题色")


class CategoryInfoCreate(CategoryInfoBase):
    sort_order: int = Field(0, description="排序顺序")
    is_active: bool = Field(True, description="是否启用")


class CategoryInfoResponse(CategoryInfoBase):
    id: int
    sort_order: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class CategoryListResponse(BaseModel):
    """类目列表响应Schema"""

    data: List[CategoryInfoResponse]
    total: int


# 添加统一响应格式
class ApiResponse(BaseModel):
    """统一的API响应格式"""

    code: int = Field(200, description="状态码")
    msg: str = Field("success", description="提示信息")
    data: Optional[Any] = Field(None, description="返回数据")


class ModuleDetailResponse(ApiResponse):
    """模块详情响应格式"""

    data: ModuleResponse
