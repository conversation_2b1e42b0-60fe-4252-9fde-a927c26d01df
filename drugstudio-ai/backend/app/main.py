# backend/app/main.py
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
import time
import logging
from app.config.settings import settings
from app.db.database import init_db, close_db_connections
from app.api.v1 import auth, users, notice, modules, favorites
from app.core.logging import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("Starting DrugStudio AI Platform...")
    await init_db()
    logger.info("Database initialized")

    yield

    # 关闭时执行
    logger.info("Shutting down DrugStudio AI Platform...")
    await close_db_connections()
    logger.info("Database connections closed")


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-powered drug design platform",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，实际生产环境中应该限制为前端域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头信息
)


# 异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": "Validation error", "errors": exc.errors()},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": "Internal server error",
            "message": str(exc) if settings.DEBUG else "An error occurred",
        },
    )


# 注册路由
# 认证和用户相关路由
app.include_router(
    auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["authentication"]
)
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])
app.include_router(
    notice.router, prefix=f"{settings.API_V1_STR}/notice", tags=["notices"]
)
# app.include_router(
#     router.router, prefix=f"{settings.API_V1_STR}/router", tags=["router"]
# )

# 通用模块相关路由
app.include_router(
    modules.router,
    prefix=f"{settings.API_V1_STR}/modules",
    tags=["modules"],
)

# 收藏相关路由
app.include_router(
    favorites.router,
    prefix=f"{settings.API_V1_STR}/favorites",
    tags=["favorites"],
)


# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "timestamp": time.time(),
    }


# 根路径
@app.get("/")
async def root():
    return {
        "message": "Welcome to DrugStudio AI Platform",
        "version": settings.APP_VERSION,
        "docs": f"{settings.API_V1_STR}/docs",
    }


# 前端兼容性路由 - 直接处理/notice/getList请求
@app.get("/notice/getList")
async def notice_list_compat():
    """处理前端的/notice/getList请求，返回模拟数据"""
    # 直接返回模拟数据，而不使用数据库和用户验证
    # 这是一个快速解决方案，避免修改前端代码

    # 使用与mock文件中相同的数据格式
    sample_notices = [
        {
            "email": "<EMAIL>",
            "image": "https://i.gtimg.cn/club/item/face/img/8/15918_100.gif",
            "notice": 'github开源地址：<a target="_blank" href="https://github.com/zxwk1998/vue-admin-better">点我</a>',
        },
        {
            "email": "<EMAIL>",
            "image": "https://i.gtimg.cn/club/item/face/img/0/15640_100.gif",
            "notice": 'Admin Pro：<a target="_blank" href="https://vuejs-core.cn/admin-pro">点我</a>',
        },
        {
            "email": "<EMAIL>",
            "image": "https://i.gtimg.cn/club/item/face/img/9/15919_100.gif",
            "notice": 'Admin Plus：<a target="_blank" href="https://vuejs-core.cn/admin-plus">点我</a>',
        },
        {
            "email": "<EMAIL>",
            "image": "https://i.gtimg.cn/club/item/face/img/8/15918_100.gif",
            "notice": 'Shop Vite：<a target="_blank" href="https://vuejs-core.cn/shop-vite">点我</a>',
        },
    ]

    # 根据错误信息，前端期望返回格式中包含list属性
    return {"data": {"list": sample_notices, "total": len(sample_notices)}}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
