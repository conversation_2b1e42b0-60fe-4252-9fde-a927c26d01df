# backend/app/core/ai/model_registry.py
from typing import Dict, List, Optional, Any
import yaml
import os
from pydantic import BaseModel
from app.config.settings import settings

class ModelMetadata(BaseModel):
    id: str
    name: str
    version: str
    type: str  # 'drug_design', 'property_prediction', 等
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    path: str
    is_active: bool = True
    gpu_required: bool = False
    memory_requirement: int = 0  # MB
    created_at: str
    updated_at: str

class ModelRegistry:
    def __init__(self):
        self.models: Dict[str, ModelMetadata] = {}
        self.registry_path = os.path.join(settings.MODEL_DIR, "registry.yaml")
        self.load_registry()
    
    def load_registry(self):
        if os.path.exists(self.registry_path):
            with open(self.registry_path, 'r') as f:
                registry_data = yaml.safe_load(f)
                for model_id, model_data in registry_data.items():
                    self.models[model_id] = ModelMetadata(**model_data)
    
    def save_registry(self):
        registry_data = {model_id: model.dict() for model_id, model in self.models.items()}
        with open(self.registry_path, 'w') as f:
            yaml.dump(registry_data, f)
    
    def register_model(self, model: ModelMetadata):
        self.models[model.id] = model
        self.save_registry()
    
    def get_model(self, model_id: str) -> Optional[ModelMetadata]:
        return self.models.get(model_id)
    
    def list_models(self, model_type: Optional[str] = None) -> List[ModelMetadata]:
        if model_type:
            return [model for model in self.models.values() if model.type == model_type]
        return list(self.models.values())
    
    def deactivate_model(self, model_id: str):
        if model_id in self.models:
            self.models[model_id].is_active = False
            self.save_registry()

# 创建全局注册表实例
model_registry = ModelRegistry()