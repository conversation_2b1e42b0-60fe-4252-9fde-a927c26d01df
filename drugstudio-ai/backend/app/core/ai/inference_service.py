# backend/app/core/ai/inference_service.py
from typing import Dict, Any, Optional
import asyncio
import logging
from app.core.ai.model_registry import model_registry, ModelMetadata
from app.core.ai.model_loader import load_model

logger = logging.getLogger(__name__)

class InferenceService:
    def __init__(self):
        self.loaded_models = {}
        self.model_locks = {}
    
    async def get_model(self, model_id: str):
        if model_id not in self.loaded_models:
            model_metadata = model_registry.get_model(model_id)
            if not model_metadata:
                raise ValueError(f"Model {model_id} not found in registry")
            
            if model_id not in self.model_locks:
                self.model_locks[model_id] = asyncio.Lock()
            
            async with self.model_locks[model_id]:
                if model_id not in self.loaded_models:  # Double check
                    logger.info(f"Loading model {model_id}")
                    self.loaded_models[model_id] = await load_model(model_metadata)
        
        return self.loaded_models[model_id]
    
    async def predict(self, model_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        model = await self.get_model(model_id)
        return await model.predict(input_data)
    
    async def unload_model(self, model_id: str):
        if model_id in self.loaded_models:
            async with self.model_locks[model_id]:
                if model_id in self.loaded_models:
                    del self.loaded_models[model_id]
                    logger.info(f"Unloaded model {model_id}")
    
    async def refresh_model(self, model_id: str):
        await self.unload_model(model_id)
        await self.get_model(model_id)

# 创建全局推理服务实例
inference_service = InferenceService()