# backend/app/db/init_modules.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.module import (
    Module,
    ModuleParameter,
    ModuleModel,
    ModuleResult,
    ModuleCategory,
    ModuleCategoryEnum,
    ModuleStatusEnum,
)
from app.models.user import User


async def init_module_categories(db: AsyncSession):
    """初始化模块类目"""
    categories_data = [
        {
            "key": "molecule_generation",
            "name": "分子生成",
            "description": "基于AI的分子生成和设计工具",
            "icon": "flask-line",
            "color": "#409EFF",
            "sort_order": 1,
        },
        {
            "key": "protein_design",
            "name": "蛋白设计",
            "description": "蛋白质结构设计和优化工具",
            "icon": "dna-line",
            "color": "#67C23A",
            "sort_order": 2,
        },
        {
            "key": "antibody_design",
            "name": "抗体设计",
            "description": "抗体药物设计和开发工具",
            "icon": "shield-line",
            "color": "#E6A23C",
            "sort_order": 3,
        },
        {
            "key": "peptide_design",
            "name": "多肽设计",
            "description": "多肽药物设计和优化工具",
            "icon": "links-line",
            "color": "#F56C6C",
            "sort_order": 4,
        },
        {
            "key": "nucleus_design",
            "name": "核酸设计",
            "description": "核酸药物设计和开发工具",
            "icon": "gene-line",
            "color": "#909399",
            "sort_order": 5,
        },
        {
            "key": "target_recognition",
            "name": "靶点识别",
            "description": "药物靶点识别和验证工具",
            "icon": "crosshair-line",
            "color": "#FF6B6B",
            "sort_order": 6,
        },
        {
            "key": "virtual_screening",
            "name": "虚拟筛选",
            "description": "化合物虚拟筛选和过滤工具",
            "icon": "filter-line",
            "color": "#4ECDC4",
            "sort_order": 7,
        },
        {
            "key": "property_prediction",
            "name": "性质预测",
            "description": "分子性质预测和分析工具",
            "icon": "line-chart-line",
            "color": "#45B7D1",
            "sort_order": 8,
        },
        {
            "key": "molecule_optimization",
            "name": "分子优化",
            "description": "分子结构优化和改进工具",
            "icon": "settings-line",
            "color": "#96CEB4",
            "sort_order": 9,
        },
        {
            "key": "molecule_docking",
            "name": "分子对接",
            "description": "分子对接和相互作用分析工具",
            "icon": "puzzle-line",
            "color": "#FFEAA7",
            "sort_order": 10,
        },
        {
            "key": "database_compound",
            "name": "数据合库",
            "description": "化合物数据库管理和查询工具",
            "icon": "database-line",
            "color": "#DDA0DD",
            "sort_order": 11,
        },
    ]

    for cat_data in categories_data:
        # 检查是否已存在
        existing = await db.execute(
            select(ModuleCategory).where(ModuleCategory.key == cat_data["key"])
        )
        if not existing.scalar_one_or_none():
            category = ModuleCategory(**cat_data)
            db.add(category)

    await db.commit()


async def init_sample_modules(db: AsyncSession):
    """初始化示例模块数据"""

    # 获取第一个用户作为创建者
    user_query = select(User).limit(1)
    result = await db.execute(user_query)
    user = result.scalar_one_or_none()
    user_id = user.id if user else 1

    modules_data = [
        {
            "module_id": "001",
            "name": "De novo Generation (Moses)",
            "short_description": "基于深度学习的分子生成模型，实现多种主流的分子生成算法",
            "description": "De novo Generation (Moses)是基于深度学习的分子生成模型，实现多种主流的分子生成算法，包括各种自编码器网络和对抗自编码器。",
            "markdown_path": "001.md",
            "category": ModuleCategoryEnum.MOLECULE_GENERATION,
            "tags": ["Deep Learning", "Moses"],
            "icon": "flask-line",
            "color": "#409EFF",
            "status": ModuleStatusEnum.ACTIVE,
            "sort_order": 1,
            "version": "1.0.0",
            "framework": "PyTorch",
            "algorithm": "VAE/AAE",
            "molecule_count_desc": "期望生成的分子数目",
            "seed_desc": "随机种子",
            "created_by": user_id,
            "updated_by": user_id,
            "parameters": [
                {
                    "name": "model_type",
                    "description": "模型类型选择，影响生成策略和结果",
                    "parameter_type": "select",
                    "is_required": True,
                },
                {
                    "name": "batch_size",
                    "description": "批处理大小，影响训练和生成速度",
                    "parameter_type": "integer",
                    "default_value": "32",
                },
                {
                    "name": "epochs",
                    "description": "训练轮次，影响模型质量",
                    "parameter_type": "integer",
                    "default_value": "100",
                },
            ],
            "models": [
                {
                    "name": "vae",
                    "description": "Variational Autoencoder (VAE) 变分自编码器",
                    "model_type": "generative",
                    "is_default": True,
                },
                {
                    "name": "aae",
                    "description": "Adversarial Autoencoder (AAE) 对抗自编码器",
                    "model_type": "generative",
                },
            ],
            "results": [
                {
                    "file_name": "result.sdf",
                    "description": "生成的sdf格式分子文件",
                    "file_type": "sdf",
                },
                {
                    "file_name": "result.csv",
                    "description": "生成的smiles格式分子文件",
                    "file_type": "csv",
                },
            ],
        },
        {
            "module_id": "002",
            "name": "De novo Generation (REINVENT4)",
            "short_description": "基于REINVENT4的分子生成，支持多种生成策略",
            "description": "基于REINVENT4的分子生成，支持多种生成策略，包括Reinvent、从头和Linkinvent等。",
            "markdown_path": "002.md",
            "category": ModuleCategoryEnum.MOLECULE_GENERATION,
            "tags": ["REINVENT4", "AI Generation"],
            "icon": "brain-line",
            "color": "#67C23A",
            "status": ModuleStatusEnum.ACTIVE,
            "sort_order": 2,
            "version": "4.0.0",
            "framework": "PyTorch",
            "algorithm": "REINVENT",
            "molecule_count_desc": "期望生成的分子数目",
            "seed_desc": "随机种子",
            "created_by": user_id,
            "updated_by": user_id,
            "parameters": [
                {
                    "name": "model_type",
                    "description": "模型类型选择",
                    "parameter_type": "select",
                    "is_required": True,
                },
                {
                    "name": "generation_strategy",
                    "description": "生成策略配置",
                    "parameter_type": "select",
                },
                {
                    "name": "sampling_method",
                    "description": "采样方法设置",
                    "parameter_type": "select",
                },
            ],
            "models": [
                {
                    "name": "Reinvent",
                    "description": "经典REINVENT模型",
                    "model_type": "generative",
                    "is_default": True,
                },
                {
                    "name": "Linkinvent",
                    "description": "连接导向的分子生成",
                    "model_type": "generative",
                },
                {
                    "name": "Libinvent",
                    "description": "基于片段的分子生成",
                    "model_type": "generative",
                },
            ],
            "results": [
                {
                    "file_name": "result.sdf",
                    "description": "生成的sdf格式分子文件",
                    "file_type": "sdf",
                },
                {
                    "file_name": "result.csv",
                    "description": "生成的smiles格式分子文件",
                    "file_type": "csv",
                },
            ],
        },
        {
            "module_id": "003",
            "name": "Lead Optimization v2",
            "short_description": "先导化合物优化，结合多种优化策略",
            "description": "先导化合物优化v2版本，结合多种优化策略，包括基于强化学习的优化、基于遗传算法的优化等。",
            "markdown_path": "003.md",
            "category": ModuleCategoryEnum.MOLECULE_OPTIMIZATION,
            "tags": ["Lead Optimization", "RL"],
            "icon": "line-chart-line",
            "color": "#E6A23C",
            "status": ModuleStatusEnum.ACTIVE,
            "sort_order": 3,
            "version": "2.0.0",
            "framework": "PyTorch",
            "algorithm": "RL/GA",
            "molecule_count_desc": "期望优化的分子数目",
            "seed_desc": "随机种子",
            "created_by": user_id,
            "updated_by": user_id,
            "parameters": [
                {
                    "name": "optimization_target",
                    "description": "优化目标设置",
                    "parameter_type": "select",
                    "is_required": True,
                },
                {
                    "name": "constraint_conditions",
                    "description": "约束条件配置",
                    "parameter_type": "text",
                },
                {
                    "name": "algorithm_selection",
                    "description": "算法选择参数",
                    "parameter_type": "select",
                },
            ],
            "models": [
                {
                    "name": "RL-based",
                    "description": "基于强化学习的优化模型",
                    "model_type": "optimization",
                    "is_default": True,
                },
                {
                    "name": "GA-based",
                    "description": "基于遗传算法的优化模型",
                    "model_type": "optimization",
                },
                {
                    "name": "Hybrid",
                    "description": "混合优化策略模型",
                    "model_type": "optimization",
                },
            ],
            "results": [
                {
                    "file_name": "optimized.sdf",
                    "description": "优化后的sdf格式分子文件",
                    "file_type": "sdf",
                },
                {
                    "file_name": "optimization_report.csv",
                    "description": "优化过程报告文件",
                    "file_type": "csv",
                },
            ],
        },
        {
            "module_id": "004",
            "name": "Scaffold Constrained Generation",
            "short_description": "保持分子骨架的约束生成",
            "description": "保持分子骨架的约束生成，能够在保持核心骨架结构的同时，对侧链进行修饰和优化。",
            "markdown_path": "004.md",
            "category": ModuleCategoryEnum.MOLECULE_GENERATION,
            "tags": ["Scaffold", "Constrained"],
            "icon": "building-line",
            "color": "#F56C6C",
            "status": ModuleStatusEnum.ACTIVE,
            "sort_order": 4,
            "version": "1.0.0",
            "framework": "PyTorch",
            "algorithm": "Scaffold-VAE",
            "molecule_count_desc": "期望生成的分子数目",
            "seed_desc": "随机种子",
            "created_by": user_id,
            "updated_by": user_id,
            "parameters": [
                {
                    "name": "scaffold_structure",
                    "description": "骨架结构定义",
                    "parameter_type": "text",
                    "is_required": True,
                },
                {
                    "name": "modification_sites",
                    "description": "修饰位点选择",
                    "parameter_type": "select",
                },
                {
                    "name": "generation_rules",
                    "description": "生成规则设置",
                    "parameter_type": "text",
                },
            ],
            "models": [
                {
                    "name": "Scaffold-VAE",
                    "description": "基于骨架的变分自编码器",
                    "model_type": "generative",
                    "is_default": True,
                },
                {
                    "name": "Fragment-based",
                    "description": "基于片段的生成模型",
                    "model_type": "generative",
                },
            ],
            "results": [
                {
                    "file_name": "scaffold_molecules.sdf",
                    "description": "保持骨架的生成分子文件",
                    "file_type": "sdf",
                },
                {
                    "file_name": "modification_summary.csv",
                    "description": "修饰位点总结文件",
                    "file_type": "csv",
                },
            ],
        },
    ]

    for module_data in modules_data:
        # 检查模块是否已存在
        existing = await db.execute(
            select(Module).where(Module.module_id == module_data["module_id"])
        )
        if existing.scalar_one_or_none():
            continue

        # 提取子数据
        parameters = module_data.pop("parameters", [])
        models = module_data.pop("models", [])
        results = module_data.pop("results", [])

        # 创建模块
        module = Module(**module_data)
        db.add(module)
        await db.flush()  # 获取模块ID

        # 创建参数
        for idx, param_data in enumerate(parameters):
            parameter = ModuleParameter(
                module_id=module.id, sort_order=idx, **param_data
            )
            db.add(parameter)

        # 创建模型
        for idx, model_data in enumerate(models):
            model = ModuleModel(module_id=module.id, sort_order=idx, **model_data)
            db.add(model)

        # 创建结果
        for idx, result_data in enumerate(results):
            result = ModuleResult(module_id=module.id, sort_order=idx, **result_data)
            db.add(result)

    await db.commit()


async def init_all_modules_data(db: AsyncSession):
    """初始化所有模块相关数据"""
    print("正在初始化模块类目...")
    await init_module_categories(db)
    print("模块类目初始化完成")

    print("正在初始化示例模块...")
    await init_sample_modules(db)
    print("示例模块初始化完成")
