from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from motor.motor_asyncio import AsyncIOMotorDatabase
import redis.asyncio as redis
from app.db.database import get_async_db, get_mongodb, get_redis


class DatabaseManager:
    """多数据库管理器"""

    @staticmethod
    async def get_databases() -> Dict[str, Any]:
        """获取所有数据库连接"""
        return {
            "postgresql": await get_async_db(),
            "mongodb": await get_mongodb(),
            "redis": await get_redis(),
        }

    @staticmethod
    def get_optimal_storage(data_type: str) -> str:
        """根据数据类型选择最优存储"""
        storage_strategy = {
            # 结构化业务数据 -> PostgreSQL
            "user_data": "postgresql",
            "permissions": "postgresql",
            "system_config": "postgresql",
            "audit_logs": "postgresql",
            # 文档和大数据 -> MongoDB
            "molecule_data": "mongodb",
            "experiment_results": "mongodb",
            "ai_outputs": "mongodb",
            "file_metadata": "mongodb",
            "analysis_reports": "mongodb",
            # 缓存和临时数据 -> Redis
            "session_data": "redis",
            "cache_data": "redis",
            "real_time_data": "redis",
            "computation_cache": "redis",
        }

        return storage_strategy.get(data_type, "postgresql")
