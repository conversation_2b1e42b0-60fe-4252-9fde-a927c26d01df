from sqlalchemy.ext.asyncio import AsyncSession
from app.models.user import User, UserRole
from app.core.security import get_password_hash


async def init_roles(db: AsyncSession):
    """初始化用户角色"""
    roles = [
        {
            "name": "admin",
            "description": "系统管理员",
            "permissions": ["read:all", "write:all", "delete:all", "manage:users"],
        },
        {
            "name": "user",
            "description": "普通用户",
            "permissions": ["read:own", "write:own"],
        },
        {
            "name": "researcher",
            "description": "研究员",
            "permissions": ["read:own", "write:own", "use:ai_models"],
        },
    ]

    for role_data in roles:
        existing_role = await db.execute(
            select(UserRole).where(UserRole.name == role_data["name"])
        )
        if not existing_role.scalars().first():
            role = UserRole(**role_data)
            db.add(role)

    await db.commit()


async def create_admin_user(db: AsyncSession):
    """创建默认管理员用户"""
    admin_role = await db.execute(select(UserRole).where(UserRole.name == "admin"))
    admin_role = admin_role.scalars().first()

    if admin_role:
        existing_admin = await db.execute(select(User).where(User.username == "admin"))
        if not existing_admin.scalars().first():
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                full_name="系统管理员",
                role_id=admin_role.id,
                is_active=True,
                is_superuser=True,
                is_verified=True,
            )
            db.add(admin_user)
            await db.commit()
