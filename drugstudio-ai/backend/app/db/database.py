# backend/app/db/database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
from app.config.settings import settings
from app.models.base import Base

# PostgreSQL 同步连接
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
)

# PostgreSQL 异步连接
async_engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

# MongoDB连接
mongodb_client = AsyncIOMotorClient(settings.MONGODB_URL)
mongodb_database = mongodb_client.drugstudio

# Redis连接
redis_client = redis.from_url(settings.REDIS_URL)


# 数据库依赖
def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        yield session


async def get_mongodb():
    return mongodb_database


async def get_redis():
    return redis_client


# 创建所有表
def create_tables():
    Base.metadata.create_all(bind=engine)


# 数据库初始化
async def init_db():
    """数据库初始化"""
    # 创建表
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 初始化基础数据
    async with AsyncSessionLocal() as session:
        # 导入初始化函数
        from app.db.init_modules import init_all_modules_data

        try:
            await init_all_modules_data(session)
            print("模块数据初始化完成")
        except Exception as e:
            print(f"模块数据初始化失败: {e}")

    print("Database initialized")


# 关闭连接
async def close_db_connections():
    await async_engine.dispose()
    mongodb_client.close()
    await redis_client.close()
