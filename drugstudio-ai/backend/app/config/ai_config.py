# backend/app/config/ai_config.py
from pydantic import BaseModel
from typing import Dict, Any, List
import os

class DrugDesignConfig(BaseModel):
    DEFAULT_MODEL: str = "mol-gen-v1"
    MAX_BATCH_SIZE: int = 32
    SUPPORTED_MODELS: List[str] = ["mol-gen-v1", "scaffold-gen-v1"]
    
    # 从环境变量读取配置
    def __init__(self, **data):
        super().__init__(**data)
        # 读取环境变量，使用前缀DRUG_DESIGN_
        prefix = "DRUG_DESIGN_"
        for field in self.__annotations__:
            env_value = os.environ.get(f"{prefix}{field.upper()}")
            if env_value is not None:
                setattr(self, field, type(getattr(self, field))(env_value))

class PropertyPredictionConfig(BaseModel):
    DEFAULT_MODEL: str = "admet-pred-v1"
    PROPERTIES: List[str] = [
        "logP", "logS", "HIA", "BBB", "CYP450", 
        "hERG", "Clearance", "Toxicity"
    ]
    
    # 从环境变量读取配置
    def __init__(self, **data):
        super().__init__(**data)
        # 读取环境变量，使用前缀PROPERTY_PRED_
        prefix = "PROPERTY_PRED_"
        for field in self.__annotations__:
            env_value = os.environ.get(f"{prefix}{field.upper()}")
            if env_value is not None:
                setattr(self, field, type(getattr(self, field))(env_value))

class TargetPredictionConfig(BaseModel):
    DEFAULT_MODEL: str = "target-pred-v1"
    SIMILARITY_THRESHOLD: float = 0.7
    
    # 从环境变量读取配置
    def __init__(self, **data):
        super().__init__(**data)
        # 读取环境变量，使用前缀TARGET_PRED_
        prefix = "TARGET_PRED_"
        for field in self.__annotations__:
            env_value = os.environ.get(f"{prefix}{field.upper()}")
            if env_value is not None:
                setattr(self, field, type(getattr(self, field))(env_value))

# 创建配置实例
drug_design_config = DrugDesignConfig()
property_prediction_config = PropertyPredictionConfig()
target_prediction_config = TargetPredictionConfig()