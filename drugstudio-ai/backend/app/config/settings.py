# backend/app/config/settings.py
from pydantic_settings import BaseSettings
from typing import Optional, List, Dict
import os


class Settings(BaseSettings):
    # 应用基础配置
    APP_NAME: str = "DrugStudio AI Platform"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    API_V1_STR: str = "/api/v1"

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8110

    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    ALGORITHM: str = "HS256"

    # 数据库配置
    DATABASE_URL: str = "postgresql://user:password@localhost:5432/drugstudio"
    MONGODB_URL: str = "****************************************"
    REDIS_URL: str = "redis://localhost:6379"

    # 文件存储配置
    UPLOAD_DIR: str = "./data/uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: List[str] = [".pdb", ".mol", ".sdf", ".xyz", ".csv", ".json"]

    # AI模型配置
    MODEL_DIR: str = "./ai_models"
    HUGGINGFACE_CACHE_DIR: str = "./cache/huggingface"
    TORCH_CACHE_DIR: str = "./cache/torch"

    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379"

    # 外部API配置
    PUBCHEM_API_URL: str = "https://pubchem.ncbi.nlm.nih.gov/rest/pug"
    CHEMBL_API_URL: str = "https://www.ebi.ac.uk/chembl/api/data"
    UNIPROT_API_URL: str = "https://rest.uniprot.org"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:5200",
        "http://127.0.0.1:5200",
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://localhost:8110",
        "http://127.0.0.1:8110",
        "http://localhost",
        "http://127.0.0.1",
        "http://*************:5200",
        "https://*************:5200",
        "http://*************",
        "https://*************",
        # 不再使用通配符，而是明确指定允许的源
    ]

    # CORS凭证设置
    CORS_ALLOW_CREDENTIALS: bool = True

    # 扩展的数据库配置
    RABBITMQ_URL: str = "amqp://user:password@localhost:5672/"

    # 数据库使用策略
    USE_MONGODB_FOR_DOCUMENTS: bool = True
    USE_REDIS_FOR_CACHE: bool = True
    USE_POSTGRESQL_FOR_RELATIONS: bool = True

    # 缓存配置
    CACHE_TTL_DEFAULT: int = 3600  # 1小时
    CACHE_TTL_LONG: int = 86400  # 1天
    CACHE_TTL_SHORT: int = 300  # 5分钟

    # MongoDB集合配置
    MONGODB_COLLECTIONS: Dict[str, str] = {
        "molecules": "molecules",
        "experiments": "experiments",
        "ai_results": "ai_results",
        "files": "file_metadata",
    }

    class Config:
        env_file = ".env"
        case_sensitive = True
        # extra = "ignore"  # 允许并忽略额外的属性


# 创建全局设置实例
settings = Settings()
