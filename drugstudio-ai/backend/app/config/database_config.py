from enum import Enum
from typing import Dict, Any


class DatabaseType(str, Enum):
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    REDIS = "redis"
    RABBITMQ = "rabbitmq"


class DatabaseConfig:
    """数据库配置管理"""

    @staticmethod
    def get_database_configs() -> Dict[str, Dict[str, Any]]:
        return {
            # PostgreSQL - 结构化数据
            "postgresql": {
                "primary_use": "结构化业务数据",
                "models": ["用户管理", "权限控制", "系统配置", "任务管理", "审计日志"],
                "sync_url": "postgresql://user:password@localhost:5432/drugstudio",
                "async_url": "postgresql+asyncpg://user:password@localhost:5432/drugstudio",
            },
            # MongoDB - 灵活文档存储
            "mongodb": {
                "primary_use": "非结构化数据和大文档",
                "collections": [
                    "分子数据",
                    "实验结果",
                    "AI模型输出",
                    "文件元数据",
                    "复杂配置",
                    "分析报告",
                ],
                "url": "****************************************",
            },
            # Redis - 缓存和会话
            "redis": {
                "primary_use": "缓存和会话管理",
                "use_cases": [
                    "用户会话",
                    "API缓存",
                    "计算结果缓存",
                    "实时数据",
                    "限流控制",
                ],
                "url": "redis://localhost:6379",
            },
            # RabbitMQ - 消息队列 (可选)
            "rabbitmq": {
                "primary_use": "消息队列和事件驱动",
                "use_cases": [
                    "AI任务调度",
                    "微服务通信",
                    "事件通知",
                    "批处理任务",
                    "数据管道",
                ],
                "url": "amqp://user:password@localhost:5672/",
            },
        }
