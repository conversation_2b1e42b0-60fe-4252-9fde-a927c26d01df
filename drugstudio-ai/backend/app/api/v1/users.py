# backend/app/api/v1/users.py
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, EmailStr, Field

from app.db.database import get_async_db
from app.api.deps import get_current_user, get_current_superuser
from app.services.user_service import UserService
from app.schemas.user import UserResponse, UserPasswordChange
from app.models.user import User
from app.core.security import verify_token, verify_password

router = APIRouter()
security = HTTPBearer()

# 用户信息更新模型
class UserUpdateRequest(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    bio: Optional[str] = None

# 管理员用户创建模型
class AdminUserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str = Field(..., min_length=6)
    full_name: Optional[str] = None
    is_superuser: bool = False
    role_id: Optional[int] = None

# 修改用户角色请求
class UserRoleUpdate(BaseModel):
    role_id: int


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    token: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
):
    """获取当前用户个人资料"""
    username = verify_token(token.credentials)
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="凭证无效",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_service = UserService(db)
    user = await user_service.get_user_by_username(username)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return await user_service.user_to_response(user)


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    update_data: UserUpdateRequest,
    token: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
):
    """更新当前用户个人资料"""
    username = verify_token(token.credentials)
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="凭证无效",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_service = UserService(db)
    user = await user_service.get_user_by_username(username)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 更新用户信息
    updated_user = await user_service.update_user(user.id, update_data.dict(exclude_unset=True))
    return await user_service.user_to_response(updated_user)


@router.post("/change-password")
async def change_password(
    password_data: UserPasswordChange,
    token: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
):
    """修改当前用户密码"""
    username = verify_token(token.credentials)
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="凭证无效",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_service = UserService(db)
    user = await user_service.get_user_by_username(username)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 验证旧密码
    if not verify_password(password_data.old_password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码不正确"
        )
    
    # 更新密码
    await user_service.change_password(user.id, password_data.old_password, password_data.new_password)
    
    return {"message": "密码修改成功"}


# ----- 管理员接口 -----

@router.get("/list", response_model=List[UserResponse])
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户列表（仅管理员）"""
    user_service = UserService(db)
    
    # 获取用户总数
    result = await db.execute(select(User))
    total = len(result.scalars().all())
    
    # 分页查询用户
    result = await db.execute(
        select(User).offset(skip).limit(limit)
    )
    users = result.scalars().all()
    
    # 转换为响应模型
    user_responses = []
    for user in users:
        user_responses.append(await user_service.user_to_response(user))
    
    return user_responses


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """获取指定用户信息（仅管理员）"""
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return await user_service.user_to_response(user)


@router.post("/create", response_model=UserResponse)
async def create_user(
    user_data: AdminUserCreate,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """创建新用户（仅管理员）"""
    user_service = UserService(db)
    
    try:
        # 创建新用户
        user = await user_service.create_admin_user(user_data.dict())
        return await user_service.user_to_response(user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}/role", response_model=UserResponse)
async def update_user_role(
    role_data: UserRoleUpdate,
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """更新用户角色（仅管理员）"""
    user_service = UserService(db)
    
    # 检查用户是否存在
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 更新用户角色
    try:
        updated_user = await user_service.update_user_role(user_id, role_data.role_id)
        return await user_service.user_to_response(updated_user)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}/activate")
async def activate_user(
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """激活用户（仅管理员）"""
    user_service = UserService(db)
    
    # 检查用户是否存在
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 激活用户
    user.is_active = True
    await db.commit()
    
    return {"message": "用户已激活"}


@router.put("/{user_id}/deactivate")
async def deactivate_user(
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_async_db)
):
    """禁用用户（仅管理员）"""
    user_service = UserService(db)
    
    # 检查用户是否存在
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能禁用自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能禁用自己的账号"
        )
    
    # 禁用用户
    user.is_active = False
    await db.commit()
    
    return {"message": "用户已禁用"}
