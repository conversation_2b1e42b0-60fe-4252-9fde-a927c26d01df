from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.database import get_async_db
from app.api.deps import get_current_user
from app.models.user import User
from app.schemas.favorite import (
    FavoriteRequest,
    FavoriteToggleResponse,
    UserFavoritesResponse,
    FavoriteStatusResponse,
)
from app.schemas.module import ModuleResponse
from app.services.favorite_service import FavoriteService
from app.services.module_service import ModuleService

router = APIRouter()


@router.post("/toggle", response_model=FavoriteToggleResponse)
async def toggle_favorite(
    request: FavoriteRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """切换模块收藏状态"""
    print(f"=== 收藏请求调试信息 ===")
    print(f"用户ID: {current_user.id}")
    print(f"用户名: {current_user.username}")
    print(f"模块ID: {request.module_id}")
    try:
        # 根据module_id获取模块
        module = await ModuleService.get_module_by_module_id(db, request.module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        # 检查当前收藏状态
        is_favorited = await FavoriteService.is_favorited(
            db, current_user.id, module.id
        )

        if is_favorited:
            # 取消收藏
            await FavoriteService.remove_favorite(db, current_user.id, module.id)
            return FavoriteToggleResponse(is_favorited=False, message="已取消收藏")
        else:
            # 添加收藏
            await FavoriteService.add_favorite(db, current_user.id, module.id)
            return FavoriteToggleResponse(is_favorited=True, message="已添加收藏")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")


@router.get("/status/{module_id}", response_model=FavoriteStatusResponse)
async def get_favorite_status(
    module_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """获取模块收藏状态"""
    try:
        # 根据module_id获取模块
        module = await ModuleService.get_module_by_module_id(db, module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        # 检查收藏状态
        is_favorited = await FavoriteService.is_favorited(
            db, current_user.id, module.id
        )

        # 获取收藏总数
        favorite_count = await FavoriteService.get_module_favorite_count(db, module.id)

        return FavoriteStatusResponse(
            module_id=module_id,
            is_favorited=is_favorited,
            favorite_count=favorite_count,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取收藏状态失败: {str(e)}")


@router.get("/my-favorites", response_model=UserFavoritesResponse)
async def get_my_favorites(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """获取当前用户的收藏列表"""
    try:
        skip = (page - 1) * size
        modules, total = await FavoriteService.get_user_favorites(
            db, current_user.id, skip, size
        )

        # 转换为前端期望的格式
        module_responses = []
        for module in modules:
            # 读取markdown内容
            markdown_content = ""
            if module.markdown_path:
                try:
                    from app.services.markdown_service import MarkdownService

                    markdown_content = await MarkdownService.read_markdown_file(
                        module.markdown_path
                    )
                except Exception as e:
                    # 如果读取失败，使用空字符串或默认内容
                    markdown_content = f"无法加载文档内容: {str(e)}"

            response_data = ModuleResponse(
                id=module.module_id,
                name=module.name,
                shortDescription=module.short_description,
                description=module.description,
                markdownContent=markdown_content,
                category=module.category,
                tags=module.tags or [],
                icon=module.icon,
                parameters=[
                    {"name": p.name, "description": p.description}
                    for p in sorted(module.parameters, key=lambda x: x.sort_order)
                ],
                models=[
                    {"name": m.name, "description": m.description}
                    for m in sorted(module.models, key=lambda x: x.sort_order)
                ],
                moleculeCount=module.molecule_count_desc or "期望生成的分子数目",
                seed=module.seed_desc or "随机种子",
                results=[
                    {"file": r.file_name, "description": r.description}
                    for r in sorted(module.results, key=lambda x: x.sort_order)
                ],
            )
            module_responses.append(response_data)

        return UserFavoritesResponse(
            data=module_responses, total=total, page=page, size=size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取收藏列表失败: {str(e)}")


@router.delete("/{module_id}")
async def remove_favorite(
    module_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """取消收藏模块"""
    try:
        # 根据module_id获取模块
        module = await ModuleService.get_module_by_module_id(db, module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        success = await FavoriteService.remove_favorite(db, current_user.id, module.id)
        if not success:
            raise HTTPException(status_code=404, detail="未找到收藏记录")

        return {"message": "已取消收藏"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消收藏失败: {str(e)}")
