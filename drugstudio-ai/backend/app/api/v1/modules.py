from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.database import get_async_db
from app.models.module import ModuleCategoryEnum, ModuleStatusEnum
from app.schemas.module import (
    ModuleResponse,
    ModuleListResponse,
    ModuleCreate,
    ModuleUpdate,
    CategoryInfoResponse,
    CategoryListResponse,
)
from app.services.module_service import ModuleService
from app.api.deps import get_current_user, get_current_user_optional
from app.models.user import User
from app.services.favorite_service import FavoriteService

router = APIRouter()


@router.get("/categories", response_model=CategoryListResponse)
async def get_categories(db: AsyncSession = Depends(get_async_db)):
    """获取所有类目信息"""
    try:
        categories = await ModuleService.get_categories(db)
        return CategoryListResponse(
            data=[CategoryInfoResponse.from_orm(cat) for cat in categories],
            total=len(categories),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取类目信息失败: {str(e)}")


@router.get("/", response_model=ModuleListResponse)
async def get_modules(
    category: Optional[ModuleCategoryEnum] = Query(None, description="按类目筛选模块"),
    status: Optional[ModuleStatusEnum] = Query(
        ModuleStatusEnum.ACTIVE, description="模块状态"
    ),
    is_featured: Optional[bool] = Query(None, description="是否为特色模块"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="跳过条数"),
    limit: int = Query(100, ge=1, le=1000, description="每页条数"),
    db: AsyncSession = Depends(get_async_db),
    current_user: Optional[User] = Depends(get_current_user_optional),  # 可选认证
):
    """获取模块列表，支持按类目筛选"""
    try:
        modules, total = await ModuleService.get_modules(
            db=db,
            category=category,
            status=status,
            is_featured=is_featured,
            search=search,
            skip=skip,
            limit=limit,
        )

        # 如果用户已登录，获取收藏状态
        favorite_status = {}
        if current_user:
            module_ids = [module.id for module in modules]
            favorite_status = await FavoriteService.batch_check_favorites(
                db, current_user.id, module_ids
            )

        # 转换为前端期望的格式
        module_responses = []
        for module in modules:
            # 获取收藏状态
            is_favorited = False
            if current_user:
                is_favorited = favorite_status.get(module.id, False)

            # 读取markdown内容
            markdown_content = ""
            if module.markdown_path:
                try:
                    from app.services.markdown_service import MarkdownService

                    print(module.markdown_path)
                    markdown_content = await MarkdownService.read_markdown_file(
                        module.markdown_path
                    )
                except Exception as e:
                    # 如果读取失败，使用空字符串或默认内容
                    markdown_content = f"无法加载文档内容: {str(e)}"

            response_data = ModuleResponse(
                id=module.module_id,
                name=module.name,
                shortDescription=module.short_description,
                description=module.description,
                markdownContent=markdown_content,
                category=module.category,
                tags=module.tags or [],
                icon=module.icon,
                parameters=[
                    {"name": p.name, "description": p.description}
                    for p in sorted(module.parameters, key=lambda x: x.sort_order)
                ],
                models=[
                    {"name": m.name, "description": m.description}
                    for m in sorted(module.models, key=lambda x: x.sort_order)
                ],
                moleculeCount=module.molecule_count_desc or "期望生成的分子数目",
                seed=module.seed_desc or "随机种子",
                results=[
                    {"file": r.file_name, "description": r.description}
                    for r in sorted(module.results, key=lambda x: x.sort_order)
                ],
                is_favorited=is_favorited,  # 在创建时就包含收藏状态
            )

            module_responses.append(response_data)

        return ModuleListResponse(data=module_responses, total=total, category=category)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get("/{module_id}")
async def get_module(
    module_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: Optional[User] = Depends(
        get_current_user_optional
    ),  # 添加可选用户认证
):
    """获取单个模块详情"""
    try:
        module = await ModuleService.get_module_by_module_id(db, module_id)

        if not module:
            return {"code": 404, "msg": "模块不存在", "data": None}

        # 检查收藏状态
        is_favorited = False
        if current_user:
            is_favorited = await FavoriteService.is_favorited(
                db, current_user.id, module.id
            )

        # 读取markdown内容
        markdown_content = ""
        if module.markdown_path:
            try:
                from app.services.markdown_service import MarkdownService

                markdown_content = await MarkdownService.read_markdown_file(
                    module.markdown_path
                )
            except Exception as e:
                # 如果读取失败，使用空字符串或默认内容
                markdown_content = f"无法加载文档内容: {str(e)}"

        # 构建响应数据，匹配前端期望的格式
        response_data = ModuleResponse(
            id=module.module_id,
            name=module.name,
            shortDescription=module.short_description,
            description=module.description,
            markdownContent=markdown_content,
            category=module.category,
            tags=module.tags or [],
            icon=module.icon,
            parameters=[
                {"name": p.name, "description": p.description}
                for p in sorted(module.parameters, key=lambda x: x.sort_order)
            ],
            models=[
                {"name": m.name, "description": m.description}
                for m in sorted(module.models, key=lambda x: x.sort_order)
            ],
            moleculeCount=module.molecule_count_desc or "期望生成的分子数目",
            seed=module.seed_desc or "随机种子",
            results=[
                {"file": r.file_name, "description": r.description}
                for r in sorted(module.results, key=lambda x: x.sort_order)
            ],
            is_favorited=is_favorited,  # 添加收藏状态
        )

        return {"code": 200, "msg": "success", "data": response_data}
    except Exception as e:
        return {"code": 500, "msg": f"获取模块详情失败: {str(e)}", "data": None}


@router.post("/{module_id}/popularity")
async def increase_module_popularity(
    module_id: str, db: AsyncSession = Depends(get_async_db)
):
    """增加模块受欢迎程度 - 无需认证"""
    try:
        module = await ModuleService.get_module_by_module_id(db, module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        updated_module = await ModuleService.increase_popularity(db, module.id)
        return {"message": "受欢迎程度已更新", "popularity": updated_module.popularity}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新受欢迎程度失败: {str(e)}")


# 以下端点需要认证（管理功能）
@router.post("/", response_model=ModuleResponse)
async def create_module(
    module_data: ModuleCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),  # 使用现有的依赖
):
    """创建模块 - 需要认证"""
    try:
        # 检查module_id是否已存在
        existing_module = await ModuleService.get_module_by_module_id(
            db, module_data.module_id
        )
        if existing_module:
            raise HTTPException(status_code=400, detail="模块ID已存在")

        module = await ModuleService.create_module(db, module_data, current_user.id)
        return ModuleResponse.from_orm(module)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建模块失败: {str(e)}")


@router.put("/{module_id}", response_model=ModuleResponse)
async def update_module(
    module_id: int,
    module_data: ModuleUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),  # 使用现有的依赖
):
    """更新模块 - 需要认证"""
    try:
        module = await ModuleService.update_module(
            db, module_id, module_data, current_user.id
        )
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        return ModuleResponse.from_orm(module)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模块失败: {str(e)}")


@router.delete("/{module_id}")
async def delete_module(
    module_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),  # 使用现有的依赖
):
    """删除模块 - 需要认证"""
    try:
        success = await ModuleService.delete_module(db, module_id)
        if not success:
            raise HTTPException(status_code=404, detail="模块不存在")

        return {"message": "模块删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模块失败: {str(e)}")
