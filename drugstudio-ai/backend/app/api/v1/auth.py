# backend/app/api/v1/auth.py
from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db.database import get_async_db, get_redis
from app.services.user_service import UserService

from app.models.user import User, UserSession
from app.schemas.user import (
    UserLogin,
    Token,
    TokenResponse,
    UserCreate,
    UserResponse,
    UserPasswordReset,
)

from app.core.security import verify_token
import redis.asyncio as redis
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()


@router.post("/register", response_model=TokenResponse)
async def register(
    user_create: UserCreate, request: Request, db: AsyncSession = Depends(get_async_db)
):
    """用户注册"""
    user_service = UserService(db)

    try:
        # 创建用户
        user = await user_service.create_user(user_create)

        # 创建会话
        session = await user_service.create_user_session(
            user_id=user.id,
            user_agent=request.headers.get("user-agent"),
            ip_address=request.client.host,
        )

        # 返回令牌（与登录接口格式保持一致）
        return {
            "data": {
                "access_token": session.session_token,
                "refresh_token": session.refresh_token,
                "token_type": "bearer",
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/login")
async def login(
    user_login: UserLogin, request: Request, db: AsyncSession = Depends(get_async_db)
):
    """用户登录 - 返回访问令牌和刷新令牌"""
    user_service = UserService(db)

    # 验证用户
    user = await user_service.authenticate_user(
        user_login.username, user_login.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="用户名或密码错误"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户已被禁用"
        )

    # 创建会话
    session = await user_service.create_user_session(
        user_id=user.id,
        user_agent=request.headers.get("user-agent"),
        ip_address=request.client.host,
    )

    # 返回令牌和用户信息（合并两种响应）
    return {
        "data": {
            "access_token": session.session_token,
            "refresh_token": session.refresh_token,
            "token_type": "bearer",
            # "user": {
            #     "id": user.id,
            #     "username": user.username,
            #     "email": user.email,
            #     "full_name": user.full_name,
            # },
        }
    }


@router.post("/refreshToken", response_model=TokenResponse)
async def refresh_token(
    refresh_token: str, request: Request, db: AsyncSession = Depends(get_async_db)
):
    """刷新令牌"""
    user_service = UserService(db)

    # 查找会话
    result = await db.execute(
        select(UserSession).where(UserSession.refresh_token == refresh_token)
    )
    session = result.scalars().first()

    if not session or not session.is_valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的刷新令牌"
        )

    # 创建新会话
    new_session = await user_service.create_user_session(
        user_id=session.user_id,
        user_agent=request.headers.get("user-agent"),
        ip_address=request.client.host,
    )

    # 使旧会话失效
    session.is_valid = False
    await db.commit()

    return {
        "data": {
            "access_token": new_session.session_token,
            "refresh_token": new_session.refresh_token,
            "token_type": "bearer",
        }
    }


@router.get("/logout")
async def logout(
    token: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db),
):
    """用户登出"""
    user_service = UserService(db)

    success = await user_service.invalidate_session(token.credentials)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="无效的会话"
        )

    return {"message": "登出成功"}


@router.get("/userInfo")
async def get_current_user_info(
    token: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db),
):
    """获取当前用户信息"""
    user_id_str = verify_token(token.credentials)
    if not user_id_str:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="凭证无效",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        user_id = int(user_id_str)
        print(f"用户ID: {user_id}")
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="令牌中的用户ID格式无效",
        )

    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 获取用户信息
    user_response = await user_service.user_to_response(user)

    # 转换为前端期望的格式
    return {
        "data": {
            "username": user_response.username,
            "avatar": user_response.avatar_url or "./static/svg/avatar.svg",
            "roles": [user_response.role] if user_response.role else [],
            "permissions": user_response.permissions,
        }
    }


@router.get("/test")
async def test_api():
    """测试API是否正常工作"""
    return {"message": "drugstudio"}


@router.post("/password", response_model=TokenResponse)
async def reset_password(
    password_data: UserPasswordReset,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """重置密码（忘记密码）"""
    user_service = UserService(db)

    # 获取用户
    user = await user_service.get_user_by_email(password_data.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="该邮箱未注册"
        )

    # 重置密码
    await user_service.reset_password_by_email(
        password_data.email, password_data.password
    )

    # 创建新会话
    session = await user_service.create_user_session(
        user_id=user.id,
        user_agent=request.headers.get("user-agent"),
        ip_address=request.client.host,
    )

    return {
        "data": {
            "access_token": session.session_token,
            "refresh_token": session.refresh_token,
            "token_type": "bearer",
        }
    }
