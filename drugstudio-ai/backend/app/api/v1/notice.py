from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas, services
from app.api import deps
from app.models.user import User
from app.models.notice import Notice

router = APIRouter()


@router.get("/getList", response_model=schemas.notice.NoticeList)
def get_notices(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取通知列表
    """
    # 检查数据库中是否有通知，如果没有则创建样例通知（仅用于演示）
    if db.query(Notice).count() == 0:
        services.notice.create_sample_notices(db)
        
    notices = services.notice.get_notices(db, skip=skip, limit=limit)
    return notices


@router.get("/{notice_id}", response_model=schemas.notice.Notice)
def get_notice(
    *,
    db: Session = Depends(deps.get_db),
    notice_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个通知详情
    """
    notice = services.notice.get_notice(db, notice_id=notice_id)
    if not notice:
        raise HTTPException(status_code=404, detail="Notice not found")
    return notice


@router.post("/", response_model=schemas.notice.Notice)
def create_notice(
    *,
    db: Session = Depends(deps.get_db),
    notice_in: schemas.notice.NoticeCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新通知
    """
    notice = services.notice.create_notice(db, obj_in=notice_in)
    return notice


@router.put("/{notice_id}/read", response_model=schemas.notice.Notice)
def mark_notice_as_read(
    *,
    db: Session = Depends(deps.get_db),
    notice_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    将通知标记为已读
    """
    notice = services.notice.mark_as_read(db, notice_id=notice_id)
    if not notice:
        raise HTTPException(status_code=404, detail="Notice not found")
    return notice


@router.delete("/{notice_id}", response_model=schemas.notice.Notice)
def delete_notice(
    *,
    db: Session = Depends(deps.get_db),
    notice_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除通知
    """
    notice = services.notice.delete_notice(db, notice_id=notice_id)
    if not notice:
        raise HTTPException(status_code=404, detail="Notice not found")
    return notice
