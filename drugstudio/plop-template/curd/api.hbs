import request from '/@/utils/request'

export const getList=(params: any) =>{
  return request({
    url: '/{{name}}/getList',
    method: 'get',
    params,
  })
}

export const doEdit=(data: any) => {
  return request({
    url: '/{{name}}/doEdit',
    method: 'post',
    data,
  })
}

export const doDelete=(data: any) =>{
  return request({
    url: '/{{name}}/doDelete',
    method: 'post',
    data,
  })
}
