<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>分栏</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="分栏">
            <rect id="矩形" fill="#F7F7F7" x="0" y="0" width="100" height="100"></rect>
            <rect id="矩形" fill="#D7D8D9" x="38" y="10" width="52" height="8" rx="2"></rect>
            <rect id="矩形备份" fill="#D7D8D9" x="38" y="21" width="24" height="25" rx="2"></rect>
            <rect id="矩形备份-2" fill="#D7D8D9" x="38" y="50" width="24" height="40" rx="2"></rect>
            <rect id="矩形备份-3" fill="#D7D8D9" x="66" y="68" width="24" height="22" rx="2"></rect>
            <rect id="矩形备份-4" fill="#D7D8D9" x="66" y="41" width="24" height="23" rx="2"></rect>
            <rect id="矩形备份-5" fill="#D7D8D9" x="66" y="21" width="24" height="16" rx="2"></rect>
            <rect id="矩形备份-6" fill="#7CADF6" x="20" y="10" width="14" height="80" rx="2"></rect>
            <rect id="矩形备份-24" fill="#D7D8D9" x="10" y="10" width="8" height="80" rx="2"></rect>
            <rect id="矩形" fill="#7CADF6" x="10" y="21" width="8" height="8"></rect>
        </g>
    </g>
</svg>