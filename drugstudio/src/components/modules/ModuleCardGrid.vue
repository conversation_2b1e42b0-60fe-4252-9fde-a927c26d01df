<template>
  <div class="module-card-grid">
    <el-row :gutter="12" style="height: 100%">
      <!-- 左侧模块卡片区域 -->
      <el-col :span="leftColumnSpan">
        <vab-card style="height: 100%" :title="cardsTitle">
          <el-row :gutter="12">
            <el-col
              v-for="(module, index) in moduleList"
              :key="index"
              :lg="8"
              :md="12"
              :sm="24"
              style="margin-bottom: 12px"
              :xl="8"
              :xs="24"
            >
              <div
                class="module-card"
                :class="{ active: selectedModule?.id === module.id }"
                @click="onCardClick(module)"
              >
                <!-- 右上角按钮组 -->
                <div class="card-actions">
                  <el-button
                    class="favorite-button"
                    :class="{ favorited: module.is_favorited }"
                    size="small"
                    @click.stop="toggleFavorite(module)"
                  >
                    <vab-icon :icon="module.is_favorited ? 'star-fill' : 'star-line'" />
                  </el-button>
                  <el-button
                    class="info-button"
                    size="small"
                    type="primary"
                    @click.stop="showModuleInfo(module)"
                  >
                    <vab-icon icon="information-line" />
                  </el-button>
                </div>

                <div class="module-content">
                  <div class="module-icon">
                    <vab-icon :icon="module.icon" />
                  </div>
                  <div class="module-info">
                    <div class="module-header">
                      <h3 class="module-title">{{ module.name }}</h3>
                    </div>
                    <p class="module-description">{{ module.shortDescription }}</p>
                    <div class="module-tags">
                      <el-tag v-for="tag in module.tags" :key="tag" type="info">
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </vab-card>
      </el-col>

      <!-- 右侧模块详情区域 -->
      <el-col :span="rightColumnSpan">
        <vab-card style="height: 100%" :title="detailsTitle">
          <div v-if="selectedModule" class="module-detail">
            <div class="detail-header">
              <div class="detail-icon">
                <vab-icon :icon="selectedModule.icon" />
              </div>
              <h2 class="detail-title">{{ selectedModule.name }}</h2>
            </div>

            <!-- 使用Markdown渲染文档内容 -->
            <div class="markdown-container" style="padding: 0">
              <v-md-editor
                v-model="selectedModule.markdownContent"
                mode="preview"
                style="padding: 0"
              />
            </div>
          </div>

          <div v-else class="no-selection">
            <div class="no-selection-icon">
              <vab-icon :icon="noSelectionIcon" />
            </div>
            <p>{{ noSelectionText }}</p>
          </div>
        </vab-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
// @ts-ignore
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
// @ts-ignore
import Prism from 'prismjs'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import type { Module } from '/@/types/moduleTypes'
import { toggleFavorite as toggleFavoriteAPI } from '/@/api/favorites'

// 条件初始化 VMdEditor，避免重复注册
if (!(globalThis as any).__VMD_EDITOR_INITIALIZED__) {
  VMdEditor.use(vuepressTheme, {
    Prism,
  })
  ;(globalThis as any).__VMD_EDITOR_INITIALIZED__ = true
}

defineOptions({
  name: 'ModuleCardGrid',
})

// 定义组件的props
const props = defineProps({
  // 模块列表数据
  moduleList: {
    type: Array as () => Module[],
    required: true,
  },
  // 左侧栏的标题
  cardsTitle: {
    type: String,
    default: '模块列表',
  },
  // 右侧栏的标题
  detailsTitle: {
    type: String,
    default: '模块详情',
  },
  // 左侧栏宽度比例
  leftColumnSpan: {
    type: Number,
    default: 14,
  },
  // 右侧栏宽度比例
  rightColumnSpan: {
    type: Number,
    default: 10,
  },

  // 未选择模块时显示的图标
  noSelectionIcon: {
    type: String,
    default: 'flask-line',
  },
  // 未选择模块时显示的文本
  noSelectionText: {
    type: String,
    default: '请选择一个模块查看详情',
  },
  // 初始选中的模块ID
  initialSelectedModuleId: {
    type: String,
    default: '',
  },
})

// 定义事件
const emit = defineEmits<{
  (e: 'select-module', module: Module): void
  (e: 'ds-module-card-click', module: Module): void
  (e: 'favorite-changed', data: { module: Module; isFavorited: boolean }): void
  (e: 'refresh-modules'): void
}>()

// 选中的模块
const selectedModule = ref<Module | null>(null)

// 点击卡片时输出到控制台
const onCardClick = (module: Module) => {
  console.log('drugstudio')
  emit('ds-module-card-click', module)
}

// 点击信息按钮时显示模块详情
const showModuleInfo = (module: Module) => {
  selectedModule.value = module
  emit('select-module', module)
}

// 切换收藏状态
const toggleFavorite = async (module: Module) => {
  try {
    const response = (await toggleFavoriteAPI(module.id)) as any

    // 更新本地模块的收藏状态
    module.is_favorited = response.is_favorited

    if (response.is_favorited) {
      $baseMessage(response.message, 'success')
    } else {
      $baseMessage(response.message, 'info')
    }

    emit('favorite-changed', { module, isFavorited: response.is_favorited })
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    $baseMessage('操作失败，请稍后重试', 'error')
  }
}

// 移除初始化默认选择的模块
onMounted(() => {
  // 不再自动选择第一个模块
  if (props.initialSelectedModuleId) {
    const initialModule = props.moduleList.find(
      (module) => module.id === props.initialSelectedModuleId
    )
    if (initialModule) {
      selectedModule.value = initialModule
    }
  }
})
</script>

<style lang="scss" scoped>
.module-card-grid {
  min-height: var(--el-container-height);
  padding: 0;
  background: #f8f9fa;

  .module-card {
    position: relative;
    height: 130px;
    padding: 14px;
    overflow: hidden;
    cursor: pointer;
    background: white;
    border: 1.5px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    &.active {
      border-color: var(--el-color-primary);
      box-shadow: 0 3px 10px rgba(78, 136, 243, 0.12);

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 3px;
        height: 100%;
        content: '';
        background-color: var(--el-color-primary);
        border-radius: 4px 0 0 4px;
      }
    }

    // 右上角按钮组
    .card-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
      display: flex;
      gap: 6px;

      .favorite-button {
        width: 28px;
        height: 28px;
        padding: 0;
        color: #909399;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        backdrop-filter: blur(4px);
        transition: all 0.2s ease;

        &:hover {
          color: #ffd700;
          background: rgba(255, 215, 0, 0.1);
          border-color: rgba(255, 215, 0, 0.3);
          transform: scale(1.05);
        }

        &.favorited {
          color: #ffd700;
          background: rgba(255, 215, 0, 0.15);
          border-color: rgba(255, 215, 0, 0.3);

          &:hover {
            background: rgba(255, 215, 0, 0.2);
          }
        }

        :deep(.vab-icon) {
          font-size: 14px;
        }
      }

      .info-button {
        width: 28px;
        height: 28px;
        padding: 0;
        color: white;
        background: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
        border-radius: 4px;
        backdrop-filter: blur(4px);
        transition: all 0.2s ease;

        &:hover {
          background: rgba(78, 136, 243, 0.8);
          border-color: rgba(78, 136, 243, 0.8);
          transform: scale(1.05);
        }

        :deep(.vab-icon) {
          font-size: 14px;
        }
      }
    }

    .module-content {
      display: flex;
      align-items: flex-start;
      height: 100%;

      .module-icon {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        font-size: 18px;
        color: var(--el-color-primary);
        background: rgba(78, 136, 243, 0.1);
        border-radius: 4px;
      }

      .module-info {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        width: calc(100% - 40px);
        margin-left: 12px;

        .module-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 4px;

          .module-title {
            margin: 0 0 4px;
            text-overflow: ellipsis;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
            color: #343a40;
            white-space: nowrap;
            // 为右上角按钮留出空间
            padding-right: 80px;
          }
        }

        .module-description {
          display: -webkit-box;
          flex-grow: 1;
          margin: 0 0 6px;
          overflow: hidden;
          -webkit-line-clamp: 2;
          font-size: 13px;
          line-height: 1.4;
          color: #6c757d;
          -webkit-box-orient: vertical;
        }

        .module-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;

          .el-tag {
            height: 20px;
            padding: 0 6px;
            font-size: 11px;
            line-height: 18px;
            color: #495057;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
          }
        }
      }
    }
  }

  .module-detail {
    .detail-header {
      display: flex;
      align-items: center;
      padding: 18px 20px;
      margin-bottom: 0;
      background: white;
      border-bottom: 1px solid #e9ecef;

      .detail-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        margin-right: 14px;
        font-size: 24px;
        color: var(--el-color-primary);
        background: rgba(78, 136, 243, 0.1);
        border-radius: 6px;
      }

      .detail-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
      }
    }

    // Markdown 容器样式
    .markdown-container {
      max-height: calc(100vh - 300px);
      padding: 20px;
      overflow-y: auto;

      :deep(.v-md-editor) {
        background: transparent;
        border: none;
        box-shadow: none;
      }

      :deep(.v-md-editor__preview-wrapper) {
        padding: 0;
      }

      :deep(.github-markdown-body) {
        padding: 0;
        background-color: transparent;
      }
    }

    .detail-actions {
      padding: 16px 20px 20px;
      margin-top: 0;
      border-top: 1px solid #e9ecef;

      .el-button {
        width: 100%;
        height: 40px;
        font-size: 14px;
        font-weight: 600;
        color: white;
        background: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(78, 136, 243, 0.8);
          border-color: rgba(78, 136, 243, 0.8);
        }

        :deep(.vab-icon) {
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  .no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    padding: 24px;
    color: #adb5bd;

    .no-selection-icon {
      margin-bottom: 16px;
      font-size: 64px;
      color: #dee2e6;
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #6c757d;
    }
  }
}

:deep(.el-card) {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

  .el-card__header {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    background: white;
    border-bottom: 1px solid #e9ecef;
  }

  .el-card__body {
    height: calc(100% - 54px);
    padding: 0;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f8f9fa;
    }

    &::-webkit-scrollbar-thumb {
      background: #dee2e6;
      border-radius: 3px;

      &:hover {
        background: #adb5bd;
      }
    }
  }
}

// 左侧卡片容器样式
:deep(.el-col:first-child .el-card) {
  .el-card__body {
    padding: 16px;
  }
}

// 右侧详情卡片样式
:deep(.el-col:last-child .el-card) {
  .el-card__body {
    padding: 0;
  }

  .el-card__header {
    display: block;
    padding: 16px 20px;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .module-card-grid {
    padding: 8px;

    .module-card {
      height: 120px;
      padding: 12px;
    }

    .detail-header {
      padding: 14px 16px !important;
    }

    .markdown-container {
      max-height: calc(100vh - 250px);
      padding: 16px !important;
    }

    .detail-actions {
      padding: 12px 16px 16px !important;
    }
  }
}
</style>
