<template>
  <div class="module-detail-panel">
      <vab-card style="height: 100%" :title="title">
        <!-- 控制按钮区域 -->
        <div class="panel-controls">
          <el-button v-if="!isShowingGeneral" circle size="small" @click="closeModule">
            <vab-icon icon="close-line" />
          </el-button>
        </div>

        <!-- 模块详情内容 -->
        <div v-if="localModule && !isShowingGeneral" class="module-detail">
          <div class="detail-header">
            <div class="detail-icon">
              <vab-icon :icon="localModule.icon" />
            </div>
            <h2 class="detail-title">{{ localModule.name }}</h2>
          </div>

          <!-- 使用Markdown渲染文档内容 -->
          <div class="markdown-container" style="padding: 0">
            <v-md-editor mode="preview" style="padding: 0" :value="localModule.markdownContent" />
          </div>
        </div>

        <!-- 通用介绍内容 -->
        <div v-else-if="isShowingGeneral" class="module-general">
          <div class="detail-header">
            <div class="detail-icon">
              <vab-icon icon="flask-line" />
            </div>
            <h2 class="detail-title">分子生成模块介绍</h2>
          </div>

          <div class="markdown-container" style="padding: 0">
            <v-md-editor v-model="generalIntroduction" mode="preview" style="padding: 0" />
          </div>
        </div>

        <!-- 无选择状态 -->
        <div v-else class="no-selection">
          <div class="no-selection-icon">
            <vab-icon :icon="noSelectionIcon" />
          </div>
          <p>{{ noSelectionText }}</p>
        </div>
      </vab-card>
  </div>
</template>

<script lang="ts" setup>
import VMdEditor from '@kangc/v-md-editor'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import '@kangc/v-md-editor/lib/theme/style/vuepress.css'
// @ts-ignore
import vuepressTheme from '@kangc/v-md-editor/lib/theme/vuepress.js'
// @ts-ignore
import Prism from 'prismjs'
import { computed, ref } from 'vue'
import type { Module } from '/@/types/moduleTypes'

// 初始化Markdown编辑器
VMdEditor.use(vuepressTheme, {
  Prism,
})

defineOptions({
  name: 'ModuleDetailPanel',
})

// 定义组件的props
const props = defineProps({
  selectedModule: {
    type: Object as () => Module | null,
    default: null,
  },
  title: {
    type: String,
    default: '模块详情',
  },

  noSelectionIcon: {
    type: String,
    default: 'information-line',
  },
  noSelectionText: {
    type: String,
    default: '请选择一个模块查看详情',
  },
})

// 定义事件
const emit = defineEmits<{
  (e: 'select-module', module: Module | null): void
}>()

// 面板状态控制
const isShowingGeneral = ref(false)

// 本地模块状态，避免直接修改props
const localModule = computed(() => props.selectedModule)

// 通用介绍内容
const generalIntroduction = ref(`# 分子生成模块

分子生成是药物设计过程中的关键步骤，通过不同的计算方法和AI技术创建新颖的化学结构。

## 主要功能

- **创建新颖分子结构**: 生成具有特定性质和活性的新分子
- **结构优化**: 基于已知活性分子进行结构优化
- **多样性分析**: 确保生成的分子具有结构多样性

## 可用模块

我们提供多种分子生成方法，包括：

1. **De novo 生成**: 从头设计新分子结构
2. **基于片段的设计**: 使用分子片段组合创建新分子
3. **深度学习方法**: 使用VAE或GAN等模型生成新结构

选择左侧的具体模块以了解更多详情和使用方法。
`)

// 关闭模块详情，显示通用介绍
const closeModule = () => {
  isShowingGeneral.value = true
  emit('select-module', null)
}

// 计算标题显示
const title = computed(() => {
  if (isShowingGeneral.value) {
    return '分子生成介绍'
  }
  return props.title
})
</script>

<style lang="scss" scoped>
.module-detail-panel {
  position: relative;
  height: 100%;

  // 控制按钮区域
  .panel-controls {
    position: absolute;
    top: 12px;
    right: 16px;
    z-index: 10;
    display: flex;
    gap: 8px;

    .el-button {
      width: 28px;
      height: 28px;
      padding: 0;
      font-size: 14px;
      border: 1px solid #e9ecef;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        color: var(--el-color-primary);
        background: #f8f9fa;
      }
    }
  }

  // 模块详情样式
  .module-detail {
    .detail-header {
      display: flex;
      align-items: center;
      padding: 18px 20px;
      margin-bottom: 0;
      background: white;
      border-bottom: 1px solid #e9ecef;

      .detail-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        margin-right: 14px;
        font-size: 24px;
        color: var(--el-color-primary);
        background: rgba(78, 136, 243, 0.1);
        border-radius: 6px;
      }

      .detail-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
      }
    }

    // Markdown 容器样式
    .markdown-container {
      max-height: calc(100vh - 300px);
      padding: 20px;
      overflow-y: auto;

      :deep(.v-md-editor) {
        background: transparent;
        border: none;
        box-shadow: none;
      }

      :deep(.v-md-editor__preview-wrapper) {
        padding: 0;
      }

      :deep(.github-markdown-body) {
        padding: 0;
        background-color: transparent;
      }
    }
  }

  // 通用介绍样式
  .module-general {
    .detail-header {
      display: flex;
      align-items: center;
      padding: 18px 20px;
      margin-bottom: 0;
      background: white;
      border-bottom: 1px solid #e9ecef;

      .detail-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        margin-right: 14px;
        font-size: 24px;
        color: var(--el-color-primary);
        background: rgba(78, 136, 243, 0.1);
        border-radius: 6px;
      }

      .detail-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
      }
    }

    .markdown-container {
      max-height: calc(100vh - 300px);
      padding: 20px;
      overflow-y: auto;

      :deep(.v-md-editor) {
        background: transparent;
        border: none;
        box-shadow: none;
      }

      :deep(.v-md-editor__preview-wrapper) {
        padding: 0;
      }

      :deep(.github-markdown-body) {
        padding: 0;
        background-color: transparent;
      }
    }
  }

  // 无选择样式
  .no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    padding: 24px;
    color: #adb5bd;

    .no-selection-icon {
      margin-bottom: 16px;
      font-size: 64px;
      color: #dee2e6;
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #6c757d;
    }
  }
}

// Card 样式覆盖
:deep(.el-card) {
  height: 100%;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

  .el-card__header {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    background: white;
    border-bottom: 1px solid #e9ecef;
  }

  .el-card__body {
    height: calc(100% - 54px);
    padding: 0;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f8f9fa;
    }

    &::-webkit-scrollbar-thumb {
      background: #dee2e6;
      border-radius: 3px;

      &:hover {
        background: #adb5bd;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .module-detail-panel {
    .detail-header {
      padding: 14px 16px !important;
    }

    .markdown-container {
      max-height: calc(100vh - 250px);
      padding: 16px !important;
    }
  }
}
</style>
