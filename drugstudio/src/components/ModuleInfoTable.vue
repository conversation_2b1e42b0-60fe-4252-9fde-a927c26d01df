<template>
  <div class="module-info-table">
    <el-table :data="[moduleData]" border stripe style="width: 100%">
      <el-table-column prop="id" label="编号" width="80" />
      <el-table-column prop="uuid" label="UUID" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip :content="row.uuid" placement="top">
            <span class="uuid-text">{{ formatUUID(row.uuid) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" width="200" show-overflow-tooltip />
      <el-table-column label="封面" width="100">
        <template #default="{ row }">
          <div class="cover-container">
            <el-image
              v-if="row.coverImage"
              :src="row.coverImage"
              :preview-src-list="[row.coverImage]"
              fit="cover"
              class="cover-image"
            />
            <div v-else class="no-cover">
              <vab-icon icon="image-line" />
              <span>暂无封面</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column label="特性" width="200">
        <template #default="{ row }">
          <div class="features-container">
            <el-tag
              v-for="feature in row.features"
              :key="feature"
              size="small"
              class="feature-tag"
            >
              {{ feature }}
            </el-tag>
            <span v-if="!row.features || row.features.length === 0" class="no-data">暂无特性</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="标签" width="200">
        <template #default="{ row }">
          <div class="tags-container">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              type="info"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <span v-if="!row.tags || row.tags.length === 0" class="no-data">暂无标签</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="author" label="作者" width="120">
        <template #default="{ row }">
          <span v-if="row.author">{{ row.author }}</span>
          <span v-else class="no-data">暂无作者</span>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" width="120">
        <template #default="{ row }">
          <span v-if="row.publishDate">{{ formatDate(row.publishDate) }}</span>
          <span v-else class="no-data">暂无时间</span>
        </template>
      </el-table-column>
      <el-table-column label="参考文献" width="150">
        <template #default="{ row }">
          <div class="references-container">
            <el-button
              v-if="row.references && row.references.length > 0"
              type="primary"
              size="small"
              @click="showReferences(row.references)"
            >
              查看文献 ({{ row.references.length }})
            </el-button>
            <span v-else class="no-data">暂无文献</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 参考文献弹窗 -->
    <el-dialog
      v-model="referencesDialogVisible"
      title="参考文献"
      width="60%"
      :before-close="handleCloseReferences"
    >
      <div class="references-list">
        <div
          v-for="(ref, index) in currentReferences"
          :key="index"
          class="reference-item"
        >
          <div class="reference-header">
            <h4>{{ ref.title }}</h4>
          </div>
          <div class="reference-details">
            <p v-if="ref.authors && ref.authors.length > 0">
              <strong>作者：</strong>{{ ref.authors.join(', ') }}
            </p>
            <p v-if="ref.journal">
              <strong>期刊：</strong>{{ ref.journal }}
            </p>
            <p v-if="ref.year">
              <strong>年份：</strong>{{ ref.year }}
            </p>
            <p v-if="ref.doi">
              <strong>DOI：</strong>
              <el-link :href="`https://doi.org/${ref.doi}`" target="_blank" type="primary">
                {{ ref.doi }}
              </el-link>
            </p>
            <p v-if="ref.url">
              <strong>链接：</strong>
              <el-link :href="ref.url" target="_blank" type="primary">
                {{ ref.url }}
              </el-link>
            </p>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="referencesDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { Module, Reference } from '/@/types/moduleTypes'

defineOptions({
  name: 'ModuleInfoTable',
})

interface Props {
  module: Module
}

const props = defineProps<Props>()

// 计算属性：格式化模块数据
const moduleData = computed(() => {
  return {
    ...props.module,
    id: props.module.id || '-',
    uuid: props.module.uuid || '-',
    name: props.module.name || '-',
    description: props.module.description || props.module.shortDescription || '-',
    features: props.module.features || [],
    tags: props.module.tags || [],
    author: props.module.author || '',
    publishDate: props.module.publishDate || '',
    references: props.module.references || [],
    coverImage: props.module.coverImage || '',
  }
})

// 参考文献弹窗
const referencesDialogVisible = ref(false)
const currentReferences = ref<Reference[]>([])

// 格式化UUID显示
const formatUUID = (uuid: string) => {
  if (!uuid) return '-'
  return uuid.length > 8 ? `${uuid.substring(0, 8)}...` : uuid
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

// 显示参考文献
const showReferences = (references: Reference[]) => {
  currentReferences.value = references
  referencesDialogVisible.value = true
}

// 关闭参考文献弹窗
const handleCloseReferences = () => {
  referencesDialogVisible.value = false
  currentReferences.value = []
}
</script>

<style lang="scss" scoped>
.module-info-table {
  .uuid-text {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
  }

  .cover-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;

    .cover-image {
      width: 60px;
      height: 60px;
      border-radius: 4px;
    }

    .no-cover {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #999;
      font-size: 12px;

      .vab-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }
    }
  }

  .features-container,
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .feature-tag,
    .tag-item {
      margin: 2px 0;
    }
  }

  .references-container {
    text-align: center;
  }

  .no-data {
    color: #999;
    font-size: 12px;
    font-style: italic;
  }
}

.references-list {
  max-height: 400px;
  overflow-y: auto;

  .reference-item {
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }

    .reference-header {
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        line-height: 1.4;
      }
    }

    .reference-details {
      p {
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.5;

        strong {
          color: #606266;
        }
      }
    }
  }
}
</style>
