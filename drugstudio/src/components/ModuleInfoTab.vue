<template>
  <div class="module-info-tab">
    <!-- 模块信息表格 -->
    <div class="module-info-section">
      <h3 class="section-title">模块信息</h3>
      <ModuleInfoTable :module="moduleWithDefaults" />
    </div>

    <!-- 统计信息 -->
    <div class="module-stats-section">
      <h3 class="section-title">统计信息</h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic suffix="种" title="支持模型" :value="module?.models?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic suffix="个" title="参数数量" :value="module?.parameters?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic suffix="个" title="结果文件" :value="module?.results?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic suffix="个" title="标签数量" :value="module?.tags?.length || 0" />
        </el-col>
      </el-row>
    </div>

    <!-- 详细描述 -->
    <div class="module-description-section">
      <h3 class="section-title">详细描述</h3>
      <div class="description-content">
        <p>{{ module?.description || '暂无详细描述' }}</p>
      </div>
    </div>

    <!-- 模型信息 -->
    <div v-if="module?.models && module.models.length > 0" class="module-models-section">
      <h3 class="section-title">支持的模型</h3>
      <el-row :gutter="16">
        <el-col
          v-for="(model, index) in module.models"
          :key="index"
          :span="12"
          class="model-item"
        >
          <el-card shadow="hover">
            <div class="model-header">
              <h4>{{ model.name }}</h4>
            </div>
            <p class="model-description">{{ model.description }}</p>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 参数信息 -->
    <div v-if="module?.parameters && module.parameters.length > 0" class="module-parameters-section">
      <h3 class="section-title">参数说明</h3>
      <el-table :data="module.parameters" border stripe>
        <el-table-column prop="name" label="参数名" width="150" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { Module } from '/@/types/moduleTypes'
import ModuleInfoTable from '/@/components/ModuleInfoTable.vue'

defineOptions({
  name: 'ModuleInfoTab',
})

interface Props {
  module: Module
  defaultFeatures?: string[]
  defaultReferences?: any[]
  defaultAuthor?: string
  defaultCoverImage?: string
}

const props = withDefaults(defineProps<Props>(), {
  defaultFeatures: () => [],
  defaultReferences: () => [],
  defaultAuthor: 'DrugStudio Team',
  defaultCoverImage: ''
})

// 为模块提供默认值，确保表格能正常显示
const moduleWithDefaults = computed(() => {
  return {
    ...props.module,
    features: props.module.features?.length > 0 ? props.module.features : props.defaultFeatures,
    author: props.module.author || props.defaultAuthor,
    publishDate: props.module.publishDate || new Date().toISOString(),
    references: props.module.references?.length > 0 ? props.module.references : props.defaultReferences,
    coverImage: props.module.coverImage || props.defaultCoverImage
  }
})
</script>

<style lang="scss" scoped>
.module-info-tab {
  .module-info-section,
  .module-stats-section,
  .module-description-section,
  .module-models-section,
  .module-parameters-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .section-title {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }
  }

  .module-stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .description-content {
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }

  .module-models-section {
    .model-item {
      margin-bottom: 16px;

      .model-header {
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .model-description {
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
        color: #606266;
      }
    }
  }
}
</style>
