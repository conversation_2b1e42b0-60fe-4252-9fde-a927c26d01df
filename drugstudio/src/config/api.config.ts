/**
 * @description 导出API配置
 */

// 使用环境变量中的基础URL
export const API_BASE_URL = import.meta.env.VITE_APP_BASE_URL || '';

// Auth endpoints - 使用相对路径
export const AUTH_API = {
  LOGIN: `${API_BASE_URL}/api/v1/auth/login`,
  REGISTER: `${API_BASE_URL}/api/v1/auth/register`,
  LOGOUT: `${API_BASE_URL}/api/v1/auth/logout`,
  REFRESH_TOKEN: `${API_BASE_URL}/api/v1/auth/refreshToken`,
  USER_INFO: `${API_BASE_URL}/api/v1/auth/userInfo`,
  RESET_PASSWORD: `${API_BASE_URL}/api/v1/auth/password`,
};

// User endpoints
export const USER_API = {
  GET_USERS: `${API_BASE_URL}/api/v1/users`,
  GET_USER: (id: string) => `${API_BASE_URL}/api/v1/users/${id}`,
  UPDATE_USER: (id: string) => `${API_BASE_URL}/api/v1/users/${id}`,
  DELETE_USER: (id: string) => `${API_BASE_URL}/api/v1/users/${id}`,
};
