import type { Component } from 'vue'

// 模块配置接口
export interface ModuleConfig {
    moduleId: string
    enabledTabs: string[]
    components: {
        run?: () => Promise<Component>
        info?: () => Promise<Component>
        docs?: () => Promise<Component>
        examples?: () => Promise<Component>
        files?: () => Promise<Component>
    }
}

// 模块配置映射
const moduleConfigs: Record<string, ModuleConfig> = {
    '001': {
        moduleId: '001',
        enabledTabs: ['run', 'info', 'docs', 'examples', 'files'],
        components: {
            run: () => import('/@/views/sysmodules/modules/001/RunComponent.vue'),
            info: () => import('/@/views/sysmodules/modules/001/InfoComponent.vue'),
            docs: () => import('/@/views/sysmodules/modules/001/DocsComponent.vue'),
            examples: () => import('/@/views/sysmodules/modules/001/ExamplesComponent.vue'),
            files: () => import('/@/views/sysmodules/modules/001/FilesComponent.vue'),
        }
    },
    '002': {
        moduleId: '002',
        enabledTabs: ['run', 'info', 'docs', 'examples'],
        components: {
            run: () => import('/@/views/sysmodules/modules/002/RunComponent.vue'),
            info: () => import('/@/views/sysmodules/modules/002/InfoComponent.vue'),
            docs: () => import('/@/views/sysmodules/modules/002/DocsComponent.vue'),
            examples: () => import('/@/views/sysmodules/modules/002/ExamplesComponent.vue'),
        }
    },
    // 可以继续添加更多模块配置...
}

// 获取模块配置
export const getModuleConfig = (moduleId: string): ModuleConfig | null => {
    return moduleConfigs[moduleId] || null
}

// 获取模块运行组件
export const getModuleRunComponent = (moduleId: string) => {
    const config = getModuleConfig(moduleId)
    return config?.components.run || null
}

// 获取模块信息组件
export const getModuleInfoComponent = (moduleId: string) => {
    const config = getModuleConfig(moduleId)
    return config?.components.info || null
}

// 获取模块Tab组件
export const getModuleTabComponent = async (moduleId: string, tabName: string): Promise<Component | null> => {
    try {
        let componentModule = null

        // 先检查是否有对应的模块和tab
        if (!moduleId || !tabName) {
            console.warn('模块ID或tab名称为空:', moduleId, tabName)
            return null
        }

        console.log(`开始加载组件: /${moduleId}/${tabName}`)

        if (moduleId === '001') {
            switch (tabName) {
                case 'run': {
                    componentModule = await import('/@/views/sysmodules/modules/001/RunTab.vue')
                    break
                }
                case 'info': {
                    componentModule = await import('/@/views/sysmodules/modules/001/InfoTab.vue')
                    break
                }
                case 'docs': {
                    componentModule = await import('/@/views/sysmodules/modules/001/DocsTab.vue')
                    break
                }
                case 'examples': {
                    componentModule = await import('/@/views/sysmodules/modules/001/ExamplesTab.vue')
                    break
                }
                case 'files': {
                    componentModule = await import('/@/views/sysmodules/modules/001/FilesTab.vue')
                    break
                }
                default: {
                    console.warn(`模块${moduleId}不支持tab: ${tabName}`)
                    return null
                }
            }
        } else if (moduleId === '002') {
            switch (tabName) {
                case 'run': {
                    componentModule = await import('/@/views/sysmodules/modules/002/RunTab.vue')
                    break
                }
                case 'info': {
                    componentModule = await import('/@/views/sysmodules/modules/002/InfoTab.vue')
                    break
                }
                case 'docs': {
                    componentModule = await import('/@/views/sysmodules/modules/002/DocsTab.vue')
                    break
                }
                case 'examples': {
                    componentModule = await import('/@/views/sysmodules/modules/002/ExamplesTab.vue')
                    break
                }
                default: {
                    console.warn(`模块${moduleId}不支持tab: ${tabName}`)
                    return null
                }
            }
        } else {
            console.warn(`不支持的模块: ${moduleId}`)
            return null
        }

        if (!componentModule) {
            console.warn(`无法加载组件: ${moduleId}/${tabName}`)
            return null
        }

        console.log(`组件加载成功: ${moduleId}/${tabName}`)
        return componentModule.default || componentModule
    } catch (error) {
        console.error(`加载模块${moduleId}的${tabName}组件失败:`, error)
        return null
    }
}

// 类似地，可以添加其他组件的获取方法... 