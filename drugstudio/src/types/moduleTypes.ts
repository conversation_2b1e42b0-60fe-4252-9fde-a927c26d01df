export interface ModuleParameter {
  name: string
  description: string
}

export interface ModuleModel {
  name: string
  description: string
}

export interface ModuleResult {
  file: string
  description: string
}

// 添加类目枚举
export enum ModuleCategory {
  MOLECULE_GENERATION = 'molecule_generation',
  PROTEIN_DESIGN = 'protein_design',
  ANTIBODY_DESIGN = 'antibody_design',
  PEPTIDE_DESIGN = 'peptide_design',
  NUCLEUS_DESIGN = 'nucleus_design',
  TARGET_RECOGNITION = 'target_recognition',
  VIRTUAL_SCREENING = 'virtual_screening',
  PROPERTY_PREDICTION = 'property_prediction',
  MOLECULE_OPTIMIZATION = 'molecule_optimization',
  MOLECULE_DOCKING = 'molecule_docking',
  DATABASE_COMPOUND = 'database_compound',
}

export interface Module {
  id: string
  name: string
  shortDescription: string
  description: string
  markdownContent: string
  icon: string
  tags: string[]
  category: ModuleCategory  // 添加类目字段
  parameters: ModuleParameter[]
  models: ModuleModel[]
  moleculeCount: string
  seed: string
  results: ModuleResult[]
  is_favorited?: boolean  // 添加收藏状态字段
}

// 添加类目信息接口
export interface CategoryInfo {
  key: string
  name: string
  icon: string
  description: string
}

// Default empty module for initialization
export const emptyModule: Module = {
  id: '',
  name: '',
  shortDescription: '',
  description: '',
  markdownContent: '',
  icon: '',
  tags: [],
  category: ModuleCategory.MOLECULE_GENERATION,
  parameters: [],
  models: [],
  moleculeCount: '',
  seed: '',
  results: [],
}
