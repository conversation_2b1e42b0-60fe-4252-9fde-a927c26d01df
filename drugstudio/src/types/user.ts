/**
 * User related type definitions
 */

export interface UserModuleType {
  token: string | boolean;
  refreshToken: string;
  username: string;
  avatar: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface UserInfoResponse {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  full_name?: string;
  avatar_url?: string;
  is_active: boolean;
  is_superuser: boolean;
  role?: string;
  permissions: string[];
}
