<template>
  <div class="module-info-test">
    <h1>模块信息表格测试</h1>
    
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="<PERSON>模块" name="001">
        <ModuleInfoTable :module="mosesModule" />
      </el-tab-pane>
      
      <el-tab-pane label="蛋白质设计" name="002">
        <ModuleInfoTable :module="proteinModule" />
      </el-tab-pane>
      
      <el-tab-pane label="分子对接" name="003">
        <ModuleInfoTable :module="dockingModule" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { Module } from '/@/types/moduleTypes'
import { ModuleCategory } from '/@/types/moduleTypes'
import ModuleInfoTable from '/@/components/ModuleInfoTable.vue'

defineOptions({
  name: 'ModuleInfoTest',
})

const activeTab = ref('001')

// 测试数据
const mosesModule: Module = {
  id: '001',
  uuid: '550e8400-e29b-41d4-a716-************',
  name: 'De novo Generation (Moses)',
  shortDescription: '基于深度学习的分子生成模型',
  description: 'De novo Generation (Moses)是基于深度学习的分子生成模型，实现多种主流的分子生成算法，包括各种自编码器网络和对抗自编码器。',
  markdownContent: '',
  coverImage: '/images/modules/moses-cover.jpg',
  features: [
    '支持多种生成模型：VAE、AAE、ORGAN、REINVENT',
    '可控的分子生成参数',
    '内置分子过滤和验证机制',
    '支持大规模批量生成',
    '结果可复现性保证'
  ],
  author: 'DrugStudio Team',
  publishDate: '2023-12-01T10:00:00Z',
  references: [
    {
      title: 'Molecular Sets (MOSES): A Benchmarking Platform for Molecular Generation Models',
      authors: ['Daniil Polykovskiy', 'Alexander Zhebrak', 'Benjamin Sanchez-Lengeling'],
      journal: 'Frontiers in Pharmacology',
      year: 2020,
      doi: '10.3389/fphar.2020.565644'
    }
  ],
  icon: 'flask-line',
  tags: ['Deep Learning', 'Moses', 'Molecular Generation'],
  category: ModuleCategory.MOLECULE_GENERATION,
  parameters: [
    { name: 'model_type', description: '模型类型选择' },
    { name: 'num_molecules', description: '生成分子数量' }
  ],
  models: [
    { name: 'VAE', description: '变分自编码器' },
    { name: 'AAE', description: '对抗自编码器' }
  ],
  moleculeCount: '期望生成的分子数目',
  seed: '随机种子',
  results: [
    { file: 'generated_molecules.sdf', description: '生成的分子文件' }
  ],
  is_favorited: false
}

const proteinModule: Module = {
  id: '002',
  uuid: '550e8400-e29b-41d4-a716-************',
  name: 'Protein Design Studio',
  shortDescription: '基于AI的蛋白质设计和优化平台',
  description: 'Protein Design Studio是一个集成了多种蛋白质设计算法的平台，支持从头设计、结构优化和功能预测。',
  markdownContent: '',
  coverImage: '/images/modules/protein-design-cover.jpg',
  features: [
    '高效的蛋白质结构预测',
    '支持多种蛋白质类型',
    '基于深度学习的算法',
    '快速计算和分析',
    '可视化结果展示'
  ],
  author: 'DrugStudio Team',
  publishDate: '2023-11-15T14:30:00Z',
  references: [
    {
      title: 'Protein Structure Prediction using Deep Learning',
      authors: ['Research Team'],
      journal: 'Nature Biotechnology',
      year: 2023,
      doi: '10.1038/s41587-023-01234-5'
    }
  ],
  icon: 'dna-line',
  tags: ['Protein Design', 'AI', 'Structure Prediction'],
  category: ModuleCategory.PROTEIN_DESIGN,
  parameters: [
    { name: 'protein_type', description: '蛋白质类型选择' },
    { name: 'sequence_length', description: '序列长度' }
  ],
  models: [
    { name: 'AlphaFold2', description: '基于深度学习的蛋白质结构预测模型' }
  ],
  moleculeCount: '期望设计的蛋白质数目',
  seed: '随机种子',
  results: [
    { file: 'protein_structure.pdb', description: '预测的蛋白质结构文件' }
  ],
  is_favorited: true
}

const dockingModule: Module = {
  id: '003',
  uuid: '550e8400-e29b-41d4-a716-************',
  name: 'Molecular Docking Pro',
  shortDescription: '高精度分子对接和虚拟筛选平台',
  description: 'Molecular Docking Pro提供高精度的分子对接算法，支持大规模虚拟筛选和药物发现。',
  markdownContent: '',
  coverImage: '/images/modules/molecular-docking-cover.jpg',
  features: [
    '智能分子对接算法',
    '高精度结合位点预测',
    '多种评分函数支持',
    '批量对接处理',
    '结果可视化分析'
  ],
  author: 'DrugStudio Team',
  publishDate: '2023-10-20T09:15:00Z',
  references: [
    {
      title: 'Advanced Molecular Docking Algorithms',
      authors: ['Computational Biology Team'],
      journal: 'Journal of Chemical Information and Modeling',
      year: 2023,
      doi: '10.1021/acs.jcim.3c00123'
    }
  ],
  icon: 'link-line',
  tags: ['Molecular Docking', 'Virtual Screening', 'Drug Discovery'],
  category: ModuleCategory.MOLECULE_DOCKING,
  parameters: [
    { name: 'receptor_file', description: '受体蛋白文件' },
    { name: 'ligand_file', description: '配体分子文件' }
  ],
  models: [
    { name: 'AutoDock Vina', description: '经典的分子对接算法' }
  ],
  moleculeCount: '期望对接的分子数目',
  seed: '随机种子',
  results: [
    { file: 'docking_results.sdf', description: '对接结果文件' }
  ],
  is_favorited: false
}
</script>

<style lang="scss" scoped>
.module-info-test {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    color: #303133;
  }
}
</style>
