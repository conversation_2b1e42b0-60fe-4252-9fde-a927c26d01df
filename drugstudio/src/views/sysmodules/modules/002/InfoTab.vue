<template>
  <div class="info-tab">
    <ModuleInfoTab
      :module="module"
      :default-features="defaultFeatures"
      :default-references="defaultReferences"
      :default-author="defaultAuthor"
      :default-cover-image="defaultCoverImage"
    />
  </div>
</template>

<script lang="ts" setup>
import type { Module } from '/@/types/moduleTypes'
import ModuleInfoTab from '/@/components/ModuleInfoTab.vue'

defineOptions({
  name: 'InfoTab',
})

defineProps<{
  module: Module
}>()

// 002模块的默认数据
const defaultFeatures = [
  '高效的蛋白质结构预测',
  '支持多种蛋白质类型',
  '基于深度学习的算法',
  '快速计算和分析',
  '可视化结果展示'
]

const defaultReferences = [
  {
    title: 'Protein Structure Prediction using Deep Learning',
    authors: ['Research Team'],
    journal: 'Nature Biotechnology',
    year: 2023,
    doi: '10.1038/s41587-023-01234-5'
  }
]

const defaultAuthor = 'DrugStudio Team'
const defaultCoverImage = '/images/modules/protein-design-cover.jpg'
</script>

<style lang="scss" scoped>
.info-tab {
  // 样式由ModuleInfoTab组件处理
}
</style>
