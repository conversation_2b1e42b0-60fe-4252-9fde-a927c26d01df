<template>
  <div class="info-tab">
    <ModuleInfoTab
      :module="module"
      :default-features="defaultFeatures"
      :default-references="defaultReferences"
      :default-author="defaultAuthor"
      :default-cover-image="defaultCoverImage"
    />
  </div>
</template>

<script lang="ts" setup>
import type { Module } from '/@/types/moduleTypes'
import ModuleInfoTab from '/@/components/ModuleInfoTab.vue'

defineOptions({
  name: 'InfoTab',
})

defineProps<{
  module: Module
}>()

// 003模块的默认数据
const defaultFeatures = [
  '智能分子对接算法',
  '高精度结合位点预测',
  '多种评分函数支持',
  '批量对接处理',
  '结果可视化分析'
]

const defaultReferences = [
  {
    title: 'Advanced Molecular Docking Algorithms',
    authors: ['Computational Biology Team'],
    journal: 'Journal of Chemical Information and Modeling',
    year: 2023,
    doi: '10.1021/acs.jcim.3c00123'
  }
]

const defaultAuthor = 'DrugStudio Team'
const defaultCoverImage = '/images/modules/molecular-docking-cover.jpg'
</script>

<style lang="scss" scoped>
.info-tab {
  // 样式由ModuleInfoTab组件处理
}
</style>
