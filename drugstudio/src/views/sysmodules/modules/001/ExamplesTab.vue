<template>
  <div class="examples-tab">
    <div class="examples-content">
      <h3>示例数据</h3>

      <!-- 示例数据展示 -->
      <div class="examples-section">
        <h4>输入示例</h4>
        <el-card class="example-card">
          <template #header>
            <div class="card-header">
              <span>分子生成参数示例</span>
              <el-button link @click="copyExample('input')">
                <vab-icon icon="file-copy-line" />
                复制
              </el-button>
            </div>
          </template>
          <pre class="example-code">{{ inputExample }}</pre>
        </el-card>
      </div>

      <div class="examples-section">
        <h4>输出示例</h4>
        <el-card class="example-card">
          <template #header>
            <div class="card-header">
              <span>生成结果示例</span>
              <el-button link @click="copyExample('output')">
                <vab-icon icon="file-copy-line" />
                复制
              </el-button>
            </div>
          </template>
          <pre class="example-code">{{ outputExample }}</pre>
        </el-card>
      </div>

      <!-- 示例文件下载 -->
      <div class="examples-section">
        <h4>示例文件</h4>
        <el-table border :data="exampleFiles" style="width: 100%">
          <el-table-column label="文件名" prop="name" />
          <el-table-column label="描述" prop="description" />
          <el-table-column label="大小" prop="size" width="100" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button link @click="downloadExample(scope.row)">
                <vab-icon icon="download-line" />
                下载
              </el-button>
              <el-button link @click="previewExample(scope.row)">
                <vab-icon icon="eye-line" />
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 使用说明 -->
      <div class="examples-section">
        <h4>使用说明</h4>
        <el-alert :closable="false" show-icon title="如何使用示例数据" type="info">
          <ul style="margin: 8px 0 0 20px; padding: 0">
            <li>下载示例文件作为输入数据的参考</li>
            <li>参考输入示例设置合适的参数</li>
            <li>查看输出示例了解期望的结果格式</li>
            <li>根据您的具体需求调整参数配置</li>
          </ul>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { Module } from '/@/types/moduleTypes'

defineOptions({
  name: 'ExamplesTab',
})

defineProps<{
  module: Module
}>()

// 示例数据
const inputExample = `{
  "job_title": "Moses分子生成示例",
  "model_type": "VAE",
  "molecule_count": 1000,
  "seed": 42,
  "temperature": 1.0,
  "batch_size": 64,
  "filter_options": [
    "druglike",
    "valid",
    "unique"
  ],
  "tags": ["Moses", "VAE", "示例"]
}`

const outputExample = `{
  "job_id": "job_001_20231201_001",
  "status": "completed",
  "generated_molecules": [
    {
      "smiles": "CCc1ccc(NC(=O)c2ccc(C)cc2)cc1",
      "druglike": true,
      "valid": true,
      "molecular_weight": 269.34,
      "logp": 3.45
    },
    {
      "smiles": "CN1CCN(C(=O)c2ccc(Cl)cc2)CC1",
      "druglike": true,
      "valid": true,
      "molecular_weight": 252.74,
      "logp": 2.18
    }
  ],
  "statistics": {
    "total_generated": 1000,
    "valid_molecules": 987,
    "druglike_molecules": 756,
    "unique_molecules": 945
  }
}`

// 示例文件列表
const exampleFiles = ref([
  {
    name: 'input_example.json',
    description: '标准输入参数示例',
    size: '2.1KB',
    type: 'input',
  },
  {
    name: 'output_example.json',
    description: '标准输出结果示例',
    size: '15.6KB',
    type: 'output',
  },
  {
    name: 'molecules_dataset.csv',
    description: '分子数据集示例',
    size: '1.2MB',
    type: 'dataset',
  },
  {
    name: 'config_template.yaml',
    description: '配置文件模板',
    size: '3.4KB',
    type: 'config',
  },
])

// 复制示例到剪贴板
const copyExample = (type: string) => {
  const text = type === 'input' ? inputExample : outputExample
  navigator.clipboard
    .writeText(text)
    .then(() => {
      $baseAlert(`${type === 'input' ? '输入' : '输出'}示例已复制到剪贴板`, 'success')
    })
    .catch(() => {
      $baseAlert('复制失败，请手动选择复制', 'error')
    })
}

// 下载示例文件
const downloadExample = (file: any) => {
  console.log('下载示例文件:', file.name)
  $baseAlert(`开始下载 ${file.name}`, 'success')
  // 这里可以实现实际的下载逻辑
}

// 预览示例文件
const previewExample = (file: any) => {
  console.log('预览示例文件:', file.name)
  $baseAlert(`预览 ${file.name}`, 'info')
  // 这里可以实现文件预览功能
}
</script>

<style lang="scss" scoped>
.examples-tab {
  .examples-content {
    h3,
    h4 {
      margin: 0 0 16px 0;
      font-weight: 600;
      color: #333;
    }

    h4 {
      font-size: 16px;
      margin-top: 32px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .examples-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .example-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .example-code {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
      margin: 0;
      font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.5;
      color: #333;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>
