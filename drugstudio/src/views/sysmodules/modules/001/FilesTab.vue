<template>
  <div class="files-tab">
    <div class="files-content">
      <h3>相关文件</h3>

      <el-table border :data="fileList" style="width: 100%">
        <el-table-column label="文件名" prop="name" />
        <el-table-column label="类型" prop="type" width="100" />
        <el-table-column label="大小" prop="size" width="100" />
        <el-table-column label="描述" prop="description" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button link @click="downloadFile(scope.row)">
              <vab-icon icon="download-line" />
              下载
            </el-button>
            <el-button link @click="viewFile(scope.row)">
              <vab-icon icon="eye-line" />
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="fileList.length === 0" style="text-align: center; padding: 48px 0">
        <el-empty description="暂无相关文件" />
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section" style="margin-top: 24px">
        <h4>上传文件</h4>
        <el-upload
          action="#"
          :auto-upload="false"
          class="upload-demo"
          drag
          multiple
          :on-change="handleFileChange"
        >
          <vab-icon icon="upload-cloud-2-line" style="font-size: 48px; color: #c0c4cc" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">支持 .txt, .csv, .xlsx, .pdf 等格式文件</div>
          </template>
        </el-upload>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { Module } from '/@/types/moduleTypes'
import type { UploadFile } from 'element-plus'

defineOptions({
  name: 'FilesTab',
})

defineProps<{
  module: Module
}>()

// 示例文件列表
const fileList = ref([
  {
    name: 'sample_input.csv',
    type: 'CSV',
    size: '2.1MB',
    description: '示例输入文件',
  },
  {
    name: 'moses_model.pkl',
    type: 'Model',
    size: '45.3MB',
    description: 'Moses预训练模型文件',
  },
  {
    name: 'documentation.pdf',
    type: 'PDF',
    size: '1.8MB',
    description: '模块使用说明文档',
  },
])

const downloadFile = (file: any) => {
  console.log('下载文件:', file.name)
  $baseAlert(`开始下载 ${file.name}`, 'success')
}

const viewFile = (file: any) => {
  console.log('查看文件:', file.name)
  $baseAlert(`查看 ${file.name}`, 'info')
}

const handleFileChange = (file: UploadFile) => {
  console.log('选择文件:', file.name)
}
</script>

<style lang="scss" scoped>
.files-tab {
  .files-content {
    h3,
    h4 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    h4 {
      font-size: 16px;
      margin-top: 32px;
    }
  }

  .upload-section {
    padding: 24px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
  }
}
</style>
