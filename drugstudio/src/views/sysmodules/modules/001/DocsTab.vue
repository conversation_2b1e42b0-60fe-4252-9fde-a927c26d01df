<template>
  <div class="docs-tab">
    <div class="help-content">
      <h3>模型说明</h3>
      <el-descriptions border :column="1">
        <el-descriptions-item label="VAE">
          变分自编码器，通过学习分子的潜在表示来生成新分子，生成质量稳定
        </el-descriptions-item>
        <el-descriptions-item label="AAE">
          对抗自编码器，结合生成对抗网络的思想，能够生成更多样化的分子
        </el-descriptions-item>
        <el-descriptions-item label="ORGAN">
          基于强化学习的分子生成模型，可以针对特定目标进行优化
        </el-descriptions-item>
        <el-descriptions-item label="REINVENT">
          基于循环神经网络的分子生成模型，适合大规模分子生成
        </el-descriptions-item>
      </el-descriptions>

      <h3 style="margin-top: 24px">参数说明</h3>
      <el-table border :data="parameterData" style="width: 100%">
        <el-table-column label="参数" prop="parameter" width="120" />
        <el-table-column label="说明" prop="description" />
        <el-table-column label="取值范围" prop="range" width="120" />
        <el-table-column label="默认值" prop="default" width="100" />
      </el-table>

      <h3 style="margin-top: 24px">注意事项</h3>
      <el-alert :closable="false" show-icon title="重要提示" type="warning">
        <ul style="margin: 0; padding-left: 20px">
          <li>分子数量越大，计算时间越长，建议根据实际需求设置</li>
          <li>温度参数影响生成多样性，过高可能导致无效分子增多</li>
          <li>建议开启类药性过滤和有效性检查以提高结果质量</li>
          <li>设置随机种子可以确保结果的可重现性</li>
        </ul>
      </el-alert>

      <!-- 如果模块有markdown内容，显示它 -->
      <div v-if="module?.markdownContent" class="markdown-content" style="margin-top: 24px">
        <h3>详细文档</h3>
        <div class="markdown-wrapper" v-html="renderedMarkdown"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { Module } from '/@/types/moduleTypes'

defineOptions({
  name: 'DocsTab',
})

const props = defineProps<{
  module: Module
}>()

// 参数说明表格数据
const parameterData = [
  {
    parameter: '分子数量',
    description: '期望生成的分子数目',
    range: '1-10000',
    default: '1000',
  },
  {
    parameter: '随机种子',
    description: '用于结果复现的随机种子',
    range: '0-999999',
    default: '随机',
  },
  {
    parameter: '温度参数',
    description: '控制生成多样性的参数',
    range: '0.1-2.0',
    default: '1.0',
  },
  {
    parameter: '批次大小',
    description: '每批处理的分子数量',
    range: '32/64/128/256',
    default: '64',
  },
]

// 简单的markdown渲染（这里可以用更完善的markdown解析器）
const renderedMarkdown = computed(() => {
  if (!props.module?.markdownContent) return ''

  // 简单的markdown渲染，实际项目中建议使用专业的markdown解析器
  return props.module.markdownContent
    .replaceAll(/### (.*)/g, '<h3>$1</h3>')
    .replaceAll(/## (.*)/g, '<h2>$1</h2>')
    .replaceAll(/# (.*)/g, '<h1>$1</h1>')
    .replaceAll(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replaceAll(/\*(.*?)\*/g, '<em>$1</em>')
    .replaceAll('\n', '<br>')
})
</script>

<style lang="scss" scoped>
.docs-tab {
  .help-content {
    h3 {
      margin: 24px 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .markdown-content {
    .markdown-wrapper {
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      line-height: 1.6;

      :deep(h1),
      :deep(h2),
      :deep(h3) {
        margin-top: 16px;
        margin-bottom: 8px;

        &:first-child {
          margin-top: 0;
        }
      }
    }
  }
}
</style>
