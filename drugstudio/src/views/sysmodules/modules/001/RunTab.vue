<template>
  <div class="run-tab">
    <!-- 这里放入原来001.vue中的提交运行部分 -->
    <el-form
      ref="formRef"
      class="module-form"
      label-width="140px"
      :model="formData"
      :rules="formRules"
    >
      <!-- 基础配置 -->
      <div class="form-section">
        <h3 class="section-title">基础配置</h3>

        <el-form-item label="任务名称" prop="jobTitle">
          <el-input
            v-model="formData.jobTitle"
            maxlength="100"
            placeholder="请输入任务名称"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="模型类型" prop="modelType">
          <el-radio-group v-model="formData.modelType">
            <el-radio value="VAE">VAE (变分自编码器)</el-radio>
            <el-radio value="AAE">AAE (对抗自编码器)</el-radio>
            <el-radio value="ORGAN">ORGAN</el-radio>
            <el-radio value="REINVENT">REINVENT</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 生成参数 -->
      <div class="form-section">
        <h3 class="section-title">生成参数</h3>

        <el-form-item label="分子数量" prop="moleculeCount">
          <el-input-number
            v-model="formData.moleculeCount"
            :max="10000"
            :min="1"
            placeholder="期望生成的分子数目"
            :step="1"
          />
          <span class="field-hint">建议范围：1-10000</span>
        </el-form-item>

        <el-form-item label="随机种子" prop="seed">
          <el-input-number
            v-model="formData.seed"
            :max="999999"
            :min="0"
            placeholder="随机种子，用于结果复现"
          />
          <span class="field-hint">可选，用于结果复现</span>
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleReset">重置参数</el-button>
      <el-button :loading="submitting" type="primary" @click="handleSubmit">提交任务</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { Module } from '/@/types/moduleTypes'

defineOptions({
  name: 'RunTab',
})

const props = defineProps<{
  module: Module
}>()

const emit = defineEmits<{
  submit: [formData: any]
  reset: []
}>()

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  jobTitle: `De novo Generation (Moses) - ${new Date().toLocaleDateString()}`,
  modelType: 'VAE',
  moleculeCount: 1000,
  seed: Math.floor(Math.random() * 999999),
})

// 表单验证规则
const formRules: FormRules = {
  jobTitle: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  modelType: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  moleculeCount: [{ required: true, message: '请输入分子数量', trigger: 'blur' }],
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      moduleId: props.module?.id,
      moduleName: props.module?.name,
      ...formData,
      timestamp: new Date().toISOString(),
    }

    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}
</script>

<style lang="scss" scoped>
.run-tab {
  .form-section {
    margin-bottom: 24px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .field-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #999;
    }
  }

  .form-actions {
    margin-top: 32px;
    text-align: center;

    .el-button {
      margin: 0 8px;
    }
  }
}
</style>
