<template>
  <div class="info-tab">
    <ModuleInfoTab
      :module="module"
      :default-features="defaultFeatures"
      :default-references="defaultReferences"
      :default-author="defaultAuthor"
      :default-cover-image="defaultCoverImage"
    />
  </div>
</template>

<script lang="ts" setup>
import type { Module } from '/@/types/moduleTypes'
import ModuleInfoTab from '/@/components/ModuleInfoTab.vue'

defineOptions({
  name: 'InfoTab',
})

defineProps<{
  module: Module
}>()

// Moses模块的默认数据
const defaultFeatures = [
  '支持多种生成模型：VAE、AAE、ORGAN、REINVENT',
  '可控的分子生成参数',
  '内置分子过滤和验证机制',
  '支持大规模批量生成',
  '结果可复现性保证'
]

const defaultReferences = [
  {
    title: 'Molecular Sets (MOSES): A Benchmarking Platform for Molecular Generation Models',
    authors: ['<PERSON><PERSON>', '<PERSON>', '<PERSON>'],
    journal: 'Frontiers in Pharmacology',
    year: 2020,
    doi: '10.3389/fphar.2020.565644'
  }
]

const defaultAuthor = 'DrugStudio Team'
const defaultCoverImage = '/images/modules/moses-cover.jpg'
</script>

<style lang="scss" scoped>
.info-tab {
  // 样式由ModuleInfoTab组件处理
}
</style>
