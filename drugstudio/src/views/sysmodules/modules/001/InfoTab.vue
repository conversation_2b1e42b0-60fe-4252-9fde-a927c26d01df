<template>
  <div class="info-tab">
    <div class="module-info-card">
      <div class="module-header">
        <div class="module-icon">
          <vab-icon icon="flask-line" />
        </div>
        <div class="module-meta">
          <h2>{{ module?.name || 'De novo Generation (Moses)' }}</h2>
          <p class="module-description">
            {{
              module?.description ||
              '基于深度学习的分子生成模型，支持多种生成策略，包括VAE、AAE、ORGAN和REINVENT等算法。可用于新药发现中的分子设计和优化。'
            }}
          </p>
        </div>
      </div>

      <div class="module-features">
        <h3>主要特性</h3>
        <ul>
          <li>支持多种生成模型：VAE、AAE、ORGAN、REINVENT</li>
          <li>可控的分子生成参数</li>
          <li>内置分子过滤和验证机制</li>
          <li>支持大规模批量生成</li>
          <li>结果可复现性保证</li>
        </ul>
      </div>

      <div class="module-stats">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-statistic suffix="种" title="支持模型" :value="4" />
          </el-col>
          <el-col :span="8">
            <el-statistic suffix="个" title="最大生成数" :value="10000" />
          </el-col>
          <el-col :span="8">
            <el-statistic suffix="分钟" title="平均耗时" :value="5" />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { Module } from '/@/types/moduleTypes'

defineOptions({
  name: 'InfoTab',
})

defineProps<{
  module: Module
}>()
</script>

<style lang="scss" scoped>
.info-tab {
  .module-info-card {
    .module-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      color: white;

      .module-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        margin-right: 20px;
        font-size: 28px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
      }

      .module-meta {
        h2 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
        }

        .module-description {
          margin: 0;
          font-size: 14px;
          line-height: 1.6;
          opacity: 0.9;
        }
      }
    }

    .module-features {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.6;
          color: #666;
        }
      }
    }

    .module-stats {
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }
  }
}
</style>
