<template>
  <div class="sys-modules-container">
    <!-- 动态组件加载 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton animated :rows="5" />
    </div>

    <component
      :is="resolvedComponent"
      v-else-if="resolvedComponent"
      :module="moduleData"
      @go-back="goBack"
      @reset="handleReset"
      @submit="handleSubmit"
    />

    <div v-else-if="!loading && moduleData" class="no-component-message">
      <el-empty description="该模块暂未配置组件" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { Module } from '/@/types/moduleTypes'
import { getModule } from '/@/api/modules'

defineOptions({
  name: 'SysModules',
})

const route = useRoute()
const router = useRouter()

// 模块数据
const moduleData = ref<Module | null>(null)
const loading = ref(false)
const resolvedComponent = ref<any>(null)

interface ModuleComponentsType {
  [key: string]: () => Promise<import('vue').Component>
  '001': () => Promise<import('vue').Component>
  '002': () => Promise<import('vue').Component>
  '003': () => Promise<import('vue').Component>
}

const moduleComponents: ModuleComponentsType = {
  '001': () => import('./001.vue'), // Moses模块
  '002': () => import('./002.vue'), // 其他模块
  '003': () => import('./003.vue'), // 其他模块
  // 可以继续添加更多模块...
}

// 动态加载组件
const loadModuleComponent = async (moduleId: string) => {
  try {
    const componentLoader = moduleComponents[moduleId]
    if (componentLoader) {
      const component = await componentLoader()
      resolvedComponent.value = component.default || component
    } else {
      resolvedComponent.value = null
    }
  } catch (error) {
    console.error('加载模块组件失败:', error)
    resolvedComponent.value = null
  }
}

// 监听路由参数变化，重新加载组件
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      loadModuleComponent(newId as string)
    }
  },
  { immediate: true }
)

// 获取模块详情
const fetchModuleData = async () => {
  try {
    loading.value = true
    const moduleId = route.params.id as string

    if (!moduleId) {
      $baseAlert('模块ID不能为空', 'error')
      goBack()
      return
    }

    const response = await getModule(moduleId)
    console.log('response', response)

    if (response && response.data) {
      moduleData.value = response.data
    } else {
      $baseAlert('模块不存在', 'error')
      goBack()
    }
  } catch (error) {
    console.error('获取模块详情失败:', error)
    $baseAlert('获取模块详情失败，请稍后重试', 'error')
    goBack()
  } finally {
    loading.value = false
  }
}

// 返回模块列表
const goBack = () => {
  // 根据模块类别返回对应的列表页面
  if (moduleData.value?.category === 'molecule_generation') {
    router.push('/navigation/molecule_generation')
  } else {
    // 可以根据其他类别添加相应的返回逻辑
    router.push('/navigation')
  }
}

// 处理表单提交
const handleSubmit = (formData: any) => {
  console.log('提交表单数据:', formData)
  // TODO: 调用API提交任务
  $baseAlert('任务提交成功！', 'success')
}

// 处理表单重置
const handleReset = () => {
  console.log('重置表单')
  $baseAlert('表单已重置', 'info')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchModuleData()
})
</script>

<style lang="scss" scoped>
.sys-modules-page {
  min-height: var(--el-container-height);
  background: white;

  .loading-container {
    padding: 24px;
  }

  .no-component-message {
    padding: 24px;
    text-align: center;
  }
}
</style>
