<template>
  <div class="moses-module">
    <!-- 顶部导航区域 -->
    <div class="module-navigation" style="padding-bottom: 5px">
      <div class="nav-left"></div>
      <div class="nav-center">
        <el-tabs v-model="activeTab" class="module-tabs" type="card">
          <el-tab-pane label="提交运行" name="config">
            <template #label>
              <div class="tab-label">
                <vab-icon icon="play-circle-line" />
                <span>提交运行</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane label="模块信息" name="details">
            <template #label>
              <div class="tab-label">
                <vab-icon icon="information-2-fill" />
                <span>模块信息</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane label="介绍文档" name="help">
            <template #label>
              <div class="tab-label">
                <vab-icon icon="chat-2-fill" />
                <span>介绍文档</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane label="文件" name="files">
            <template #label>
              <div class="tab-label">
                <vab-icon icon="file-copy-2-fill" />
                <span>文件</span>
                <!-- <div class="p-badge">1</div> -->
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="nav-right">
        <el-button class="back-button" @click="handleGoBack">
          <vab-icon icon="arrow-left-line" />
          返回
        </el-button>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content-wrapper">
      <!-- 模块详情标签页 -->
      <div v-show="activeTab === 'details'" class="tab-content">
        <div class="module-info-card">
          <div class="module-header">
            <div class="module-icon">
              <vab-icon icon="flask-line" />
            </div>
            <div class="module-meta">
              <h2>De novo Generation (Moses)</h2>
              <p class="module-description">
                基于深度学习的分子生成模型，支持多种生成策略，包括VAE、AAE、ORGAN和REINVENT等算法。
                可用于新药发现中的分子设计和优化。
              </p>
            </div>
          </div>

          <div class="module-features">
            <h3>主要特性</h3>
            <ul>
              <li>支持多种生成模型：VAE、AAE、ORGAN、REINVENT</li>
              <li>可控的分子生成参数</li>
              <li>内置分子过滤和验证机制</li>
              <li>支持大规模批量生成</li>
              <li>结果可复现性保证</li>
            </ul>
          </div>

          <div class="module-stats">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-statistic suffix="种" title="支持模型" :value="4" />
              </el-col>
              <el-col :span="8">
                <el-statistic suffix="个" title="最大生成数" :value="10000" />
              </el-col>
              <el-col :span="8">
                <el-statistic suffix="分钟" title="平均耗时" :value="5" />
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 参数配置标签页 -->
      <div v-show="activeTab === 'config'" class="tab-content">
        <el-form
          ref="formRef"
          class="module-form"
          label-width="140px"
          :model="formData"
          :rules="formRules"
        >
          <!-- 基础配置 -->
          <div class="form-section">
            <h3 class="section-title">基础配置</h3>

            <el-form-item label="任务名称" prop="jobTitle">
              <el-input
                v-model="formData.jobTitle"
                maxlength="100"
                placeholder="请输入任务名称"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="模型类型" prop="modelType">
              <el-radio-group v-model="formData.modelType">
                <el-radio label="VAE">VAE (变分自编码器)</el-radio>
                <el-radio label="AAE">AAE (对抗自编码器)</el-radio>
                <el-radio label="ORGAN">ORGAN</el-radio>
                <el-radio label="REINVENT">REINVENT</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 生成参数 -->
          <div class="form-section">
            <h3 class="section-title">生成参数</h3>

            <el-form-item label="分子数量" prop="moleculeCount">
              <el-input-number
                v-model="formData.moleculeCount"
                :max="10000"
                :min="1"
                placeholder="期望生成的分子数目"
                :step="1"
              />
              <span class="field-hint">建议范围：1-10000</span>
            </el-form-item>

            <el-form-item label="随机种子" prop="seed">
              <el-input-number
                v-model="formData.seed"
                :max="999999"
                :min="0"
                placeholder="随机种子，用于结果复现"
              />
              <span class="field-hint">可选，用于结果复现</span>
            </el-form-item>

            <el-form-item label="温度参数" prop="temperature">
              <el-slider
                v-model="formData.temperature"
                :input-size="'small'"
                :max="2.0"
                :min="0.1"
                show-input
                :step="0.1"
              />
              <span class="field-hint">控制生成多样性，值越高越多样</span>
            </el-form-item>
          </div>

          <!-- 高级选项 -->
          <div class="form-section">
            <h3 class="section-title">高级选项</h3>

            <el-form-item label="批次大小" prop="batchSize">
              <el-select v-model="formData.batchSize" placeholder="选择批次大小">
                <el-option label="32" :value="32" />
                <el-option label="64" :value="64" />
                <el-option label="128" :value="128" />
                <el-option label="256" :value="256" />
              </el-select>
              <span class="field-hint">影响生成速度和内存使用</span>
            </el-form-item>

            <el-form-item label="过滤条件" prop="filterOptions">
              <el-checkbox-group v-model="formData.filterOptions">
                <el-checkbox label="druglike">类药性过滤</el-checkbox>
                <el-checkbox label="valid">有效性检查</el-checkbox>
                <el-checkbox label="unique">去重处理</el-checkbox>
                <el-checkbox label="novelty">新颖性检查</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- 标签 -->
          <div class="form-section">
            <h3 class="section-title">任务标签</h3>

            <el-form-item label="标签" prop="tags">
              <el-tag
                v-for="tag in formData.tags"
                :key="tag"
                class="tag-item"
                closable
                @close="removeTag(tag)"
              >
                {{ tag }}
              </el-tag>

              <el-input
                v-if="tagInputVisible"
                ref="tagInputRef"
                v-model="tagInputValue"
                class="tag-input"
                size="small"
                @blur="addTag"
                @keyup.enter="addTag"
              />

              <el-button v-else class="add-tag-btn" size="small" @click="showTagInput">
                + 添加标签
              </el-button>
            </el-form-item>
          </div>
        </el-form>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="handleReset">重置参数</el-button>
          <el-button :loading="submitting" type="primary" @click="handleSubmit">提交任务</el-button>
        </div>
      </div>

      <!-- 使用说明标签页 -->
      <div v-show="activeTab === 'help'" class="tab-content">
        <div class="help-content">
          <h3>模型说明</h3>
          <el-descriptions border :column="1">
            <el-descriptions-item label="VAE">
              变分自编码器，通过学习分子的潜在表示来生成新分子，生成质量稳定
            </el-descriptions-item>
            <el-descriptions-item label="AAE">
              对抗自编码器，结合生成对抗网络的思想，能够生成更多样化的分子
            </el-descriptions-item>
            <el-descriptions-item label="ORGAN">
              基于强化学习的分子生成模型，可以针对特定目标进行优化
            </el-descriptions-item>
            <el-descriptions-item label="REINVENT">
              基于循环神经网络的分子生成模型，适合大规模分子生成
            </el-descriptions-item>
          </el-descriptions>

          <h3 style="margin-top: 24px">参数说明</h3>
          <el-table border :data="parameterData" style="width: 100%">
            <el-table-column label="参数" prop="parameter" width="120" />
            <el-table-column label="说明" prop="description" />
            <el-table-column label="取值范围" prop="range" width="120" />
            <el-table-column label="默认值" prop="default" width="100" />
          </el-table>

          <h3 style="margin-top: 24px">注意事项</h3>
          <el-alert :closable="false" show-icon title="重要提示" type="warning">
            <ul style="margin: 0; padding-left: 20px">
              <li>分子数量越大，计算时间越长，建议根据实际需求设置</li>
              <li>温度参数影响生成多样性，过高可能导致无效分子增多</li>
              <li>建议开启类药性过滤和有效性检查以提高结果质量</li>
              <li>设置随机种子可以确保结果的可重现性</li>
            </ul>
          </el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { Module } from '/@/types/moduleTypes'

defineOptions({
  name: 'MosesModule',
})

const props = defineProps<{
  module: Module | null
}>()

const emit = defineEmits<{
  submit: [formData: any]
  reset: []
  goBack: []
}>()

// 当前激活的标签页
const activeTab = ref('details')

// 表单引用
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 标签输入相关
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref()

// 参数说明表格数据
const parameterData = [
  {
    parameter: '分子数量',
    description: '期望生成的分子数目',
    range: '1-10000',
    default: '1000',
  },
  {
    parameter: '随机种子',
    description: '用于结果复现的随机种子',
    range: '0-999999',
    default: '随机',
  },
  {
    parameter: '温度参数',
    description: '控制生成多样性的参数',
    range: '0.1-2.0',
    default: '1.0',
  },
  {
    parameter: '批次大小',
    description: '每批处理的分子数量',
    range: '32/64/128/256',
    default: '64',
  },
]

// 表单数据
const formData = reactive({
  jobTitle: `De novo Generation (Moses) - ${new Date().toLocaleDateString()}`,
  modelType: 'VAE',
  moleculeCount: 1000,
  seed: Math.floor(Math.random() * 999999),
  temperature: 1,
  batchSize: 64,
  filterOptions: ['druglike', 'valid'],
  tags: ['Moses', 'De novo'],
})

// 表单验证规则
const formRules: FormRules = {
  jobTitle: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '任务名称长度应在1-100字符之间', trigger: 'blur' },
  ],
  modelType: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  moleculeCount: [
    { required: true, message: '请输入分子数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '分子数量应在1-10000之间', trigger: 'blur' },
  ],
  temperature: [
    { required: true, message: '请设置温度参数', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 2, message: '温度参数应在0.1-2.0之间', trigger: 'blur' },
  ],
  batchSize: [{ required: true, message: '请选择批次大小', trigger: 'change' }],
}

// 添加标签
const addTag = () => {
  if (tagInputValue.value && !formData.tags.includes(tagInputValue.value)) {
    formData.tags.push(tagInputValue.value)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

// 移除标签
const removeTag = (tag: string) => {
  const index = formData.tags.indexOf(tag)
  if (index !== -1) {
    formData.tags.splice(index, 1)
  }
}

// 显示标签输入框
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      moduleId: props.module?.id,
      moduleName: props.module?.name,
      ...formData,
      timestamp: new Date().toISOString(),
    }

    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 重置为默认值
  Object.assign(formData, {
    jobTitle: `De novo Generation (Moses) - ${new Date().toLocaleDateString()}`,
    modelType: 'VAE',
    moleculeCount: 1000,
    seed: Math.floor(Math.random() * 999999),
    temperature: 1,
    batchSize: 64,
    filterOptions: ['druglike', 'valid'],
    tags: ['Moses', 'De novo'],
  })

  emit('reset')
}

// 添加返回处理函数
const handleGoBack = () => {
  emit('goBack')
}
</script>

<style lang="scss" scoped>
.moses-module {
  .module-navigation {
    display: flex;
    align-items: center;
    padding: 0;
    padding-bottom: 0;
    background: var(--el-color-white);
    border-bottom: 1px solid var(--el-border-color);
    margin-bottom: 0;

    .nav-left {
      flex-shrink: 0;
      margin-right: 0;
    }

    .nav-center {
      flex: 1;

      .module-tabs {
        :deep(.el-tabs__header) {
          margin: 0;
          border-bottom: none;
        }

        :deep(.el-tabs__nav-wrap) {
          &::after {
            display: none;
          }
        }

        :deep(.el-tabs__nav) {
          border: none;
          border-radius: 0;
        }

        :deep(.el-tabs__item) {
          border: 1px solid #d9d9d9;
          border-bottom: none;
          border-radius: 4px 4px 0 0;
          margin-right: 2px;
          padding: 0 16px;
          height: 40px;
          line-height: 38px;
          background: #fafafa;
          color: #666;
          transition: all 0.3s;

          &:hover {
            color: #409eff;
          }

          &.is-active {
            background: #fff;
            color: #409eff;
            // border-color: #409eff;
            border-bottom: 1px solid #fff;
            position: relative;
            z-index: 1;

            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              right: 0;
              height: 1px;
              background: #fff;
            }
          }

          .tab-label {
            display: flex;
            align-items: center;
            gap: 6px;

            i {
              font-size: 14px;
            }

            .p-badge {
              background: #409eff;
              color: white;
              font-size: 12px;
              padding: 1px 6px;
              border-radius: 10px;
              min-width: 16px;
              height: 16px;
              line-height: 14px;
              text-align: center;
              margin-left: 4px;
            }
          }
        }
      }
    }

    .nav-right {
      flex-shrink: 0;
      width: 120px;
      display: flex;
      justify-content: flex-end;

      .back-button {
        color: #666;
        border: 1px solid #dcdfe6;

        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
    }
  }

  .tab-content-wrapper {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top: none;

    .tab-content {
      padding: 20px;
    }

    // 模块信息卡片
    .module-info-card {
      .module-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        color: white;

        .module-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          margin-right: 20px;
          font-size: 28px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
        }

        .module-meta {
          h2 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
          }

          .module-description {
            margin: 0;
            font-size: 14px;
            line-height: 1.6;
            opacity: 0.9;
          }
        }
      }

      .module-features {
        margin-bottom: 24px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: #666;
          }
        }
      }

      .module-stats {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
      }
    }

    // 表单样式
    .module-form {
      .form-section {
        margin-bottom: 32px;

        .section-title {
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #212529;
          border-bottom: 2px solid #e9ecef;
        }

        .field-hint {
          margin-left: 8px;
          font-size: 12px;
          color: #999;
        }
      }

      .tag-item {
        margin-right: 8px;
        margin-bottom: 8px;
      }

      .tag-input {
        width: 120px;
        margin-right: 8px;
      }

      .add-tag-btn {
        border-style: dashed;
      }
    }

    .form-actions {
      padding-top: 24px;
      border-top: 1px solid #e9ecef;
      text-align: right;

      .el-button {
        margin-left: 12px;
      }
    }

    // 帮助内容样式
    .help-content {
      h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }
}
</style>
