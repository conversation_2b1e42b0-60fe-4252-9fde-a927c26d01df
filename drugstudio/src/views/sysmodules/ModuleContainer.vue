<template>
  <div class="module-container">
    <!-- 动态组件加载 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton animated :rows="5" />
    </div>

    <div v-else-if="moduleData" class="module-content">
      <!-- 顶部导航区域 -->
      <div class="module-navigation">
        <div class="nav-left">
          <el-tabs
            v-model="activeTab"
            class="module-tabs"
            type="card"
            @tab-change="handleTabChange"
          >
            <el-tab-pane
              v-for="tab in availableTabs"
              :key="tab.name"
              :label="tab.label"
              :name="tab.name"
            >
              <template #label>
                <div class="tab-label">
                  <vab-icon :icon="tab.icon" />
                  <span>{{ tab.label }}</span>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="nav-center"></div>
        <div class="nav-right">
          <el-button class="back-button" @click="handleGoBack">
            <vab-icon icon="arrow-left-line" />
            返回
          </el-button>
        </div>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content-wrapper">
        <!-- 动态加载tab内容 -->
        <component
          :is="currentTabComponent"
          v-if="currentTabComponent"
          :module="moduleData"
          @reset="handleReset"
          @submit="handleSubmit"
        />
        <div v-else class="no-tab-content">
          <el-empty description="该Tab暂未配置内容" />
          <!-- 调试信息 -->
          <div style="margin-top: 16px; font-size: 12px; color: #999">
            调试信息: 模块ID: {{ moduleData.id }}, 当前Tab: {{ activeTab }}
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-module-message">
      <el-empty description="模块不存在或暂未配置" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, markRaw, nextTick, onMounted, ref, shallowRef, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { Module } from '/@/types/moduleTypes'
import { getModule } from '/@/api/modules'
import { getModuleTabComponent } from '/@/config/moduleConfig'
import type { TabPaneName } from 'element-plus'

defineOptions({
  name: 'ModuleContainer',
})

const route = useRoute()
const router = useRouter()

// 模块数据
const moduleData = ref<Module | null>(null)
const loading = ref(false)

// Tab配置
const allTabs = [
  { name: 'run', label: '提交运行', icon: 'play-circle-line' },
  { name: 'info', label: '模块信息', icon: 'information-2-fill' },
  { name: 'docs', label: '介绍文档', icon: 'chat-2-fill' },
  { name: 'examples', label: '示例数据', icon: 'database-2-line' },
  { name: 'files', label: '文件', icon: 'file-copy-2-fill' },
]

// 当前激活的标签页
const activeTab = ref('run')

// 可用的标签页（根据模块配置）
const availableTabs = computed(() => {
  // 显示所有5个tab
  return allTabs
})

// 当前tab组件 - 使用shallowRef避免组件被reactive化
const currentTabComponent = shallowRef(null)

// 获取模块详情
const fetchModuleData = async () => {
  try {
    loading.value = true
    const moduleId = route.params.id as string

    if (!moduleId) {
      $baseAlert('模块ID不能为空', 'error')
      handleGoBack()
      return
    }

    const response = await getModule(moduleId)

    if (response && response.data) {
      moduleData.value = response.data

      // 等待DOM更新
      await nextTick()

      // 获取URL中的tab参数，如果没有则默认为run
      const tabParam = (route.params.tab as string) || 'run'
      const validTab = availableTabs.value.some((t) => t.name === tabParam) ? tabParam : 'run'

      activeTab.value = validTab

      // 如果URL中没有tab参数，更新URL
      if (!route.params.tab || route.params.tab !== validTab) {
        router.replace(`/system-modules/${moduleId}/${validTab}`)
      }

      // 加载对应的tab组件
      await loadTabComponent()
    } else {
      $baseAlert('模块不存在', 'error')
      handleGoBack()
    }
  } catch (error) {
    console.error('获取模块详情失败:', error)
    $baseAlert('获取模块详情失败，请稍后重试', 'error')
    handleGoBack()
  } finally {
    loading.value = false
  }
}

// 加载tab组件
const loadTabComponent = async () => {
  if (!moduleData.value || !activeTab.value) {
    console.warn('模块数据或activeTab为空')
    return
  }

  try {
    console.log('加载tab组件:', moduleData.value.id, activeTab.value)
    currentTabComponent.value = null // 先清空

    const component = await getModuleTabComponent(moduleData.value.id, activeTab.value)

    if (component) {
      // 使用markRaw标记组件为非响应式
      currentTabComponent.value = markRaw(component)
      console.log('tab组件加载成功:', activeTab.value)
    } else {
      console.warn('tab组件加载失败，返回null')
    }
  } catch (error) {
    console.error('加载tab组件失败:', error)
    currentTabComponent.value = null
  }
}

// 标签页切换
const handleTabChange = async (tabName: TabPaneName) => {
  const tabNameStr = String(tabName)
  console.log('切换到tab:', tabNameStr)

  activeTab.value = tabNameStr
  router.replace(`/system-modules/${route.params.id}/${tabNameStr}`)
  await loadTabComponent()
}

// 返回模块列表
const handleGoBack = () => {
  if (moduleData.value?.category === 'molecule_generation') {
    router.push('/navigation/molecule_generation')
  } else {
    router.push('/navigation')
  }
}

// 处理表单提交
const handleSubmit = (formData: any) => {
  console.log('提交表单数据:', formData)
  $baseAlert('任务提交成功！', 'success')
}

// 处理表单重置
const handleReset = () => {
  console.log('重置表单')
  $baseAlert('表单已重置', 'info')
}

// 监听路由参数变化
watch(
  () => [route.params.id, route.params.tab],
  async ([newId, newTab]) => {
    console.log('路由参数变化:', newId, newTab)

    if (newId && newId !== moduleData.value?.id) {
      console.log('模块ID变化，重新获取模块数据')
      await fetchModuleData()
    } else if (newTab && newTab !== activeTab.value && moduleData.value) {
      console.log('Tab变化，切换tab')
      activeTab.value = newTab as string
      await loadTabComponent()
    }
  },
  { immediate: true }
)

// 组件挂载时获取数据
onMounted(() => {
  console.log('ModuleContainer mounted, 当前路由参数:', route.params)
})
</script>

<style lang="scss" scoped>
.module-container {
  min-height: var(--el-container-height);
  background: white;

  .loading-container {
    padding: 24px;
  }

  .no-module-message,
  .no-tab-content {
    padding: 24px;
    text-align: center;
  }

  .module-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    border-bottom: 1px solid var(--el-border-color-light);

    .nav-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .module-tabs {
      .tab-label {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    .back-button {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .tab-content-wrapper {
    padding: 24px;
  }
}
</style>
