<template>
  <login-container>
    <div class="login-form">
      <img alt="" class="left-img" :src="leftImg" />
      <el-form ref="formRef" label-position="left" :model="form" :rules="rules" @submit.prevent>
        <div class="title">hello !</div>
        <div class="title-tips">{{ title }} {{ translate('账号注册') }}</div>
        <el-form-item prop="username">
          <el-input
            v-model.trim="form.username"
            v-focus
            auto-complete="off"
            clearable
            :placeholder="translate('请输入用户名')"
            type="text"
          >
            <template #prefix>
              <vab-icon icon="user-line" />
            </template>
          </el-input>
        </el-form-item>
        <!-- Phone field removed -->
        <!-- <el-form-item prop="phone">
          <el-input
            v-model.trim="form.phone"
            clearable
            maxlength="11"
            :placeholder="translate('请输入手机号')"
            show-word-limit
            type="text"
          >
            <template #prefix>
              <vab-icon icon="smartphone-line" />
            </template>
          </el-input>
        </el-form-item> -->
        
        <!-- Email field -->
        <el-form-item prop="email">
          <el-input
            v-model.trim="form.email"
            clearable
            :placeholder="translate('请输入邮箱')"
            type="email"
          >
            <template #prefix>
              <vab-icon icon="mail-line" />
            </template>
          </el-input>
        </el-form-item>
        <!-- Phone verification code removed -->
        <!-- <el-form-item prop="phoneCode" style="position: relative">
          <el-input
            v-model.trim="form.phoneCode"
            :placeholder="translate('请输入手机验证码')"
            type="text"
          >
            <template #prefix>
              <vab-icon icon="barcode-box-line" />
            </template>
          </el-input>
          <el-button class="phone-code" :disabled="isGetPhone" type="primary" @click="getPhoneCode">
            {{ translate(phoneCode) }}
          </el-button>
        </el-form-item> -->
        <el-form-item prop="password">
          <el-input
            v-model.trim="form.password"
            clearable
            :placeholder="translate('请输入密码')"
            type="password"
          >
            <template #prefix>
              <vab-icon icon="lock-line" />
            </template>
          </el-input>
        </el-form-item>
        
        <!-- Confirm password field -->
        <el-form-item prop="confirmPassword">
          <el-input
            v-model.trim="form.confirmPassword"
            clearable
            :placeholder="translate('请再次输入密码')"
            type="password"
          >
            <template #prefix>
              <vab-icon icon="lock-password-line" />
            </template>
          </el-input>
        </el-form-item>
        <el-button
          v-throttle="handleRegister"
          class="login-btn"
          :loading="loading"
          native-type="submit"
          type="primary"
        >
          {{ translate('注册') }}
        </el-button>
        <router-link to="/login">
          <el-button style="margin-top: 20px; margin-left: -10px" type="primary">
            {{ translate('登录') }}
          </el-button>
        </router-link>
        <router-link to="/password">
          <el-button style="margin-top: 20px" text type="primary">
            {{ translate('忘记密码') }}
          </el-button>
        </router-link>
      </el-form>
    </div>
  </login-container>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import { register } from '/@/api/user'
import leftImg from '/@/assets/login_images/left_img_1.png'
import { translate } from '/@/i18n'
import { useSettingsStore } from '/@/store/modules/settings'
import { useUserStore } from '/@/store/modules/user'
import { isPassword } from '/@/utils/validate'
import { gp } from '/@vab/plugins/vab'

defineOptions({
  name: 'Register',
})

interface FormType {
  username: string
  password: string
  confirmPassword: string
  email: string
  phone: string
  verificationCode: string
  phoneCode: string
}

const router = useRouter()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const { title } = storeToRefs(settingsStore)
const { setToken } = userStore
const loading = ref<boolean>(false)
const formRef = ref<FormInstance>()
// The following variables are no longer used but kept for reference
// const isGetPhone = ref<boolean>(false)
// const phoneCode = ref<any>(translate('获取验证码'))
const form = reactive<FormType>({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  phone: '',
  verificationCode: '',
  phoneCode: '',
})

const validateUsername = (rule: any, value: any, callback: any) => {
  if ('' === value) callback(new Error(translate('用户名不能为空')))
  else callback()
}
const validatePassword = (rule: any, value: any, callback: any) => {
  if (isPassword(value)) {
    callback()
  } else {
    callback(new Error(translate('密码不能少于6位')))
  }
}
// Phone validation function removed
/* const validatePhone = (rule: any, value: any, callback: any) => {
  if (isPhone(value)) {
    callback()
  } else {
    callback(new Error(translate('请输入正确的手机号')))
  }
} */

const rules = reactive<FormRules<FormType>>({
  username: [
    {
      required: true,
      trigger: 'blur',
      message: translate('请输入用户名'),
    },
    { validator: validateUsername, trigger: 'blur' },
  ],
  email: [
    {
      required: true,
      trigger: 'blur',
      message: translate('请输入邮箱'),
    },
    { type: 'email', message: translate('请输入正确的邮箱格式'), trigger: 'blur' },
  ],
  // Phone validation removed
  // phone: [
  //   {
  //     required: false,
  //     trigger: 'blur',
  //     message: translate('请输入手机号'),
  //   },
  //   { validator: validatePhone, trigger: 'blur' },
  // ],
  password: [
    {
      required: true,
      trigger: 'blur',
      message: translate('请输入密码'),
    },
    { validator: validatePassword, trigger: 'blur' },
  ],
  confirmPassword: [
    {
      required: true,
      trigger: 'blur',
      message: translate('请再次输入密码'),
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== form.password) {
          callback(new Error(translate('两次输入的密码不一致')))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  // phoneCode validation removed since the field is commented out
  // phoneCode: [
  //   {
  //     required: true,
  //     trigger: 'blur',
  //     message: translate('请输入手机验证码'),
  //   },
  // ],
})

// Phone verification code function commented out since it's no longer needed
/* const getPhoneCode = () => {
  if (!isPhone(form.phone)) {
    formRef.value?.validateField('phone')
    return
  }
  isGetPhone.value = true
  let n = 60
  timer = setInterval(() => {
    if (n > 0) {
      n--
      phoneCode.value = `${translate('获取验证码 ') + n}s`
    } else {
      clearInterval(timer)
      phoneCode.value = translate('获取验证码')
      isGetPhone.value = false
    }
  }, 1000)
} */
const handleRegister = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      loading.value = true
      // Create a registration payload without phone number field
      const registerData = {
        username: form.username,
        password: form.password,
        email: form.email
        // phone field removed
      }
      
      try {
        const response: any = await register(registerData)
        
        // 处理注册成功后的响应
        // Mock环境和真实API环境的响应格式可能不同
        if (response.code === 200 && response.data?.token) {
          // Mock环境的响应格式: { code: 200, msg: '模拟注册成功', data: { token } }
          const token = response.data.token
          setToken(token)
          
          const settingsStore = useSettingsStore()
          const hour = new Date().getHours()
          const thisTime = hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
          
          gp.$baseNotify(`注册成功，欢迎加入${settingsStore.title}`, `${thisTime}！`)
          gp.$baseMessage(response.msg || '注册成功', 'success')
          
          // 跳转到首页
          await router.push('/index')
        } else if (response.access_token) {
          // 真实API环境的响应格式: { access_token, refresh_token, token_type }
          userStore.afterLogin(response)
          await router.push('/index')
        } else {
          // 其他情况使用 afterLogin 方法统一处理
          userStore.afterLogin(response)
          await router.push('/index')
        }
      } catch (error: any) {
        console.error('Registration error:', error)
        // 错误信息已经在 request.ts 中的响应拦截器中处理了
        // 这里只需要重置loading状态
      } finally {
        loading.value = false
      }
    }
  })
}

// No more timer to clear
onUnmounted(() => {
  // clearInterval(timer)
})
</script>
