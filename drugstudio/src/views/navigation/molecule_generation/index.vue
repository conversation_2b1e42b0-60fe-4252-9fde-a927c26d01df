<template>
  <div class="molecule-generation">
    <module-card-grid
      key="molecule-generation"
      cards-title="分子生成模块"
      details-title="模块详情"
      :left-column-span="14"
      :module-list="moduleList"
      no-selection-icon="flask-line"
      no-selection-text="请选择一个模块查看详情"
      :right-column-span="10"
      @ds-module-card-click="handleCardClick"
      @select-module="handleSelectModule"
    />
  </div>
</template>

<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { Module } from '/@/types/moduleTypes'
import { ModuleCategory } from '/@/types/moduleTypes'
import { getModules } from '/@/api/modules' // 使用新的通用API
import ModuleCardGrid from '/@/components/modules/ModuleCardGrid.vue'

defineOptions({
  name: 'MoleculeGeneration',
})

const router = useRouter()

// 模块列表
const moduleList = ref<Module[]>([])
const loading = ref(false)

// 获取分子生成类目的模块列表
const fetchModuleList = async () => {
  try {
    loading.value = true
    const response = await getModules(ModuleCategory.MOLECULE_GENERATION)

    if (response && response.data) {
      moduleList.value = response.data
    }
  } catch (error) {
    console.error('获取分子生成模块列表失败:', error)
    $baseAlert('获取模块列表失败，请稍后重试', 'error')
  } finally {
    loading.value = false
  }
}

// 处理info按钮点击 - 展示右侧模块详情
const handleSelectModule = (module: Module) => {
  console.log(`展示模块详情: ${module.name}`)
  // 这里不需要额外处理，ModuleCardGrid组件内部已经处理了右侧展示
}

// 处理卡片点击 - 跳转到模块详情页面
const handleCardClick = (module: Module) => {
  console.log(`跳转到模块详情页面: ${module.name}`, module)
  // 跳转到模块详情页面
  router.push({
    path: `/system-modules/${module.id}`,
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchModuleList()
})

// 页面激活时重新获取数据（保持数据最新，特别是收藏状态）
onActivated(() => {
  console.log('分子生成页面被激活，重新获取数据')
  fetchModuleList()
})
</script>

<style lang="scss" scoped>
.molecule-generation {
  min-height: var(--el-container-height);
  padding: 0;
  background: #f8f9fa;
}
</style>
