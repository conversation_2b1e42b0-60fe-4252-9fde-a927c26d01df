<template>
  <div class="my-favorite">
    <module-card-grid
      key="my-favorite"
      cards-title="我的收藏"
      details-title="模块详情"
      :left-column-span="14"
      :module-list="favoriteModules"
      no-selection-icon="star-line"
      no-selection-text="请选择一个模块查看详情"
      :right-column-span="10"
      @select-module="handleSelectModule"
      @favorite-changed="handleFavoriteChanged"
      @refresh-modules="fetchFavoriteModules"
      @ds-module-card-click="handleCardClick" 
    />
    
    <!-- 空状态展示 -->
    <div v-if="!loading && favoriteModules.length === 0" class="empty-state">
      <div class="empty-icon">
        <vab-icon icon="star-line" />
      </div>
      <h3 class="empty-title">暂无收藏的模块</h3>
      <p class="empty-description">
        您还没有收藏任何模块。去其他页面找找您感兴趣的模块，点击星标即可收藏！
      </p>
      <div class="empty-actions">
        <el-button type="primary" @click="goToModules">
          <vab-icon icon="compass-3-line" />
          浏览模块
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state" v-loading="loading" element-loading-text="正在加载收藏的模块...">
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import type { Module } from '/@/types/moduleTypes'
import { getMyFavorites, removeFavorite } from '/@/api/favorites'
import ModuleCardGrid from '/@/components/modules/ModuleCardGrid.vue'

defineOptions({
  name: 'MyFavorite',
})

const router = useRouter()

// 收藏的模块列表
const favoriteModules = ref<Module[]>([])
const loading = ref(false)

// 获取我的收藏列表
const fetchFavoriteModules = async () => {
  try {
    loading.value = true
    const response = await getMyFavorites()
    
    if (response && response.data) {
      // 确保所有收藏的模块都标记为已收藏
      favoriteModules.value = response.data.map((module: Module) => ({
        ...module,
        is_favorited: true
      }))
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    $baseAlert('获取收藏列表失败，请稍后重试', 'error')
  } finally {
    loading.value = false
  }
}

// 处理模块选择事件
const handleSelectModule = (module: Module) => {
  console.log(`已选择收藏模块: ${module.name}`)
}

const handleCardClick = (module: Module) => {
  console.log(`跳转到模块详情页面: ${module.name}`, module)
  // 跳转到模块详情页面
  router.push({
    path: `/system-modules/${module.id}`,
  })
}

// 处理收藏状态变化
const handleFavoriteChanged = async (data: { module: Module; isFavorited: boolean }) => {
  const { module, isFavorited } = data
  
  if (!isFavorited) {
    // 如果取消收藏，从列表中移除该模块
    favoriteModules.value = favoriteModules.value.filter(m => m.id !== module.id)
    $baseMessage(`已取消收藏 ${module.name}`, 'info')
  }
}

// 跳转到模块浏览页面
const goToModules = () => {
  // 跳转到分子生成页面作为示例
  router.push('/navigation/molecule_generation')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchFavoriteModules()
})

// 页面激活时重新获取数据（解决缓存问题）
onActivated(() => {
  console.log('收藏页面被激活，重新获取数据')
  fetchFavoriteModules()
})
</script>

<style lang="scss" scoped>
.my-favorite {
  min-height: var(--el-container-height);
  padding: 0;
  background: #f8f9fa;
  position: relative;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 60px 20px;
  text-align: center;

  .empty-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin-bottom: 24px;
    font-size: 40px;
    color: #d0d7de;
    background: #f6f8fa;
    border-radius: 50%;
  }

  .empty-title {
    margin: 0 0 12px;
    font-size: 20px;
    font-weight: 600;
    color: #24292f;
  }

  .empty-description {
    max-width: 480px;
    margin: 0 0 32px;
    font-size: 16px;
    line-height: 1.6;
    color: #656d76;
  }

  .empty-actions {
    display: flex;
    gap: 16px;
    justify-content: center;

    .el-button {
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

// 当有收藏内容时隐藏空状态
.my-favorite:has(.module-card-grid .module-card) .empty-state {
  display: none;
}
</style>
