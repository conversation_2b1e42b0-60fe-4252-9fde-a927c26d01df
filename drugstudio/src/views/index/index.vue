<template>
  <div class="index-container">
    <!-- Features Section -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">全面的药物设计功能</h2>
        <p class="section-subtitle">
          我们的平台结合了前沿的AI技术与制药专业知识，简化药物发现过程的每个环节
        </p>
      </div>

      <div class="features-grid">
        <div
          v-for="(feature, index) in features"
          :key="index"
          class="feature-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="feature-icon">
            <vab-icon :icon="feature.icon" />
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'Index',
})

interface FeatureCard {
  icon: string
  title: string
  description: string
}

const features: FeatureCard[] = [
  {
    icon: 'brain-line',
    title: 'AI智能预测',
    description: '利用深度学习模型预测分子结构和性质，提供卓越的准确性和可靠性。',
  },
  {
    icon: 'dna-line',
    title: '分子优化',
    description: '在保持合成可行性的同时，优化候选药物的理想性质。',
  },
  {
    icon: 'test-tube-line',
    title: '虚拟筛选',
    description: '在计算机中筛选数百万化合物，更快地识别有前景的候选药物。',
  },
  {
    icon: 'search-eye-line',
    title: '靶点识别',
    description: '使用我们全面的蛋白质-配体相互作用数据库识别潜在的药物靶点。',
  },
  {
    icon: 'compass-3-line',
    title: 'ADMET预测',
    description: '在设计过程早期预测吸收、分布、代谢、排泄和毒性性质。',
  },
  {
    icon: 'line-chart-line',
    title: '定量分析',
    description: '为您的化合物生成详细的定量构效关系(QSAR)模型。',
  },
  {
    icon: 'microscope-line',
    title: '研究协作',
    description: '通过我们的安全平台与不同地点的团队成员无缝协作。',
  },
  {
    icon: 'database-2-line',
    title: '综合数据库',
    description: '访问我们广泛的化合物、结构和性质数据库，加速您的研究。',
  },
]
</script>

<style lang="scss" scoped>
.index-container {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

// Hero Section
.hero-section {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 100vh;
  padding: 0 5%;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 25%,
    #f1f5f9 50%,
    #e2e8f0 75%,
    #cbd5e1 100%
  );

  .hero-background {
    position: absolute;
    inset: 0;
    opacity: 0.08;

    .bg-circle {
      position: absolute;
      border-radius: 50%;
      filter: blur(100px);
      animation: float 8s ease-in-out infinite;

      &.bg-circle-1 {
        top: -200px;
        left: -200px;
        width: 400px;
        height: 400px;
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        animation-delay: 0s;
      }

      &.bg-circle-2 {
        top: 50%;
        right: -150px;
        width: 350px;
        height: 350px;
        background: linear-gradient(45deg, #06b6d4, #3b82f6);
        animation-delay: 2s;
      }

      &.bg-circle-3 {
        bottom: -100px;
        left: 30%;
        width: 300px;
        height: 300px;
        background: linear-gradient(45deg, #8b5cf6, #ec4899);
        animation-delay: 4s;
      }
    }
  }

  .hero-content {
    position: relative;
    z-index: 10;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    width: 80%;
    max-width: 1400px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .hero-text {
    .hero-title {
      margin-bottom: 32px;
      line-height: 1.1;

      .hero-brand {
        display: block;
        margin-bottom: 16px;
        font-size: 4rem;
        font-weight: 800;
        color: #1e293b;

        @media (max-width: 768px) {
          font-size: 3rem;
        }
      }

      .hero-subtitle {
        display: block;
        font-size: 2.5rem;
        font-weight: 600;
        color: #475569;

        @media (max-width: 768px) {
          font-size: 2rem;
        }

        .hero-highlight {
          font-weight: 700;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .hero-description {
      max-width: 600px;
      margin-bottom: 40px;
      font-size: 1.25rem;
      line-height: 1.7;
      color: #64748b;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      align-items: center;

      @media (max-width: 640px) {
        flex-direction: column;
        width: 100%;
      }

      .hero-btn-primary {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
          transform: translateY(-3px);
        }

        .ml-2 {
          margin-left: 8px;
          transition: transform 0.3s ease;
        }

        &:hover .ml-2 {
          transform: translateX(4px);
        }
      }

      .hero-btn-secondary {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        color: #475569;
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(71, 85, 105, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          color: #1e293b;
          background: rgba(255, 255, 255, 0.95);
          border-color: rgba(71, 85, 105, 0.4);
          transform: translateY(-3px);
        }
      }
    }
  }

  .hero-visual {
    display: flex;
    justify-content: center;

    .molecule-card {
      width: 100%;
      max-width: 450px;
      padding: 32px;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(71, 85, 105, 0.1);
      border-radius: 24px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(20px);
      transition: all 0.5s ease;

      &:hover {
        box-shadow: 0 35px 70px rgba(0, 0, 0, 0.12);
        transform: translateY(-10px) scale(1.02);
      }

      .molecule-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        aspect-ratio: 1;
        margin-bottom: 24px;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
        border-radius: 20px;

        &::before {
          position: absolute;
          inset: 0;
          content: '';
          background: linear-gradient(
            45deg,
            transparent 30%,
            rgba(255, 255, 255, 0.1) 50%,
            transparent 70%
          );
          animation: shimmer 3s ease-in-out infinite;
        }

        .molecule-center {
          position: relative;
          z-index: 3;
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          border-radius: 50%;
          box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
          animation: pulse 2s ease-in-out infinite;
        }

        .molecule-orbit {
          position: absolute;
          border: 2px solid rgba(59, 130, 246, 0.4);
          border-radius: 50%;

          &.orbit-1 {
            width: 80px;
            height: 80px;
            animation: spin 10s linear infinite;
          }

          &.orbit-2 {
            width: 120px;
            height: 80px;
            transform: rotate(45deg);
            animation: spin 15s linear infinite reverse;
          }
        }

        .molecule-atoms {
          position: absolute;
          width: 80px;
          height: 80px;

          .atom {
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            animation: atomFloat 3s ease-in-out infinite;

            &.atom-1 {
              top: -8px;
              left: 50%;
              background: linear-gradient(135deg, #ec4899, #f97316);
              box-shadow: 0 0 15px rgba(236, 72, 153, 0.6);
              transform: translateX(-50%);
            }

            &.atom-2 {
              bottom: -8px;
              left: 50%;
              background: linear-gradient(135deg, #06b6d4, #3b82f6);
              box-shadow: 0 0 15px rgba(6, 182, 212, 0.6);
              transform: translateX(-50%);
              animation-delay: 0.5s;
            }

            &.atom-3 {
              top: 50%;
              left: -8px;
              background: linear-gradient(135deg, #8b5cf6, #3b82f6);
              box-shadow: 0 0 15px rgba(139, 92, 246, 0.6);
              transform: translateY(-50%);
              animation-delay: 1s;
            }

            &.atom-4 {
              top: 50%;
              right: -8px;
              background: linear-gradient(135deg, #10b981, #06b6d4);
              box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
              transform: translateY(-50%);
              animation-delay: 1.5s;
            }
          }
        }
      }

      .molecule-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .stat-item {
          padding: 16px;
          text-align: center;
          background: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(71, 85, 105, 0.1);
          border-radius: 12px;

          .stat-value {
            margin-bottom: 4px;
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
          }

          .stat-label {
            font-size: 0.875rem;
            color: #64748b;
          }
        }
      }
    }
  }
}

// Features Section
.features-section {
  padding: 60px 5% 80px;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);

  .section-header {
    margin-bottom: 80px;
    text-align: center;

    .section-title {
      margin-bottom: 24px;
      font-size: 3rem;
      font-weight: 700;
      color: #1e293b;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }

    .section-subtitle {
      max-width: 800px;
      margin: 0 auto;
      font-size: 1.25rem;
      line-height: 1.6;
      color: #64748b;
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    max-width: 1400px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .feature-card {
    padding: 40px 32px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.4s ease;
    animation: fadeInUp 0.6s ease forwards;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      transform: translateY(-8px);

      .feature-icon {
        color: white;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        transform: scale(1.1) rotate(5deg);
      }
    }

    .feature-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 54px;
      height: 54px;
      margin-bottom: 24px;
      color: #3b82f6;
      background: linear-gradient(135deg, #eff6ff, #dbeafe);
      border-radius: 16px;
      transition: all 0.4s ease;

      :deep(.vab-icon) {
        font-size: 40px;
      }
    }

    .feature-title {
      margin-bottom: 16px;
      font-size: 1.5rem;
      font-weight: 600;
      line-height: 1.3;
      color: #1e293b;
    }

    .feature-description {
      margin: 0;
      font-size: 1rem;
      line-height: 1.6;
      color: #64748b;
    }
  }
}

// Dashboard Section
.dashboard-section {
  padding: 80px 5%;
  background: #f8fafc;

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .dashboard-item {
      &.full-width {
        grid-column: 1 / -1;
      }

      :deep(.el-card) {
        border: 1px solid #e2e8f0;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
        }
      }
    }
  }
}

// 动画定义
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    transform: scale(1.1);
  }
}

@keyframes atomFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计优化
@media (max-width: 480px) {
  .hero-section {
    padding: 0 3%;

    .hero-content {
      gap: 40px;
      width: 95%;
    }

    .hero-text {
      .hero-title {
        .hero-brand {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.8rem;
        }
      }

      .hero-description {
        font-size: 1rem;
      }
    }
  }

  .features-section {
    padding: 40px 3% 60px;

    .section-header {
      margin-bottom: 60px;

      .section-title {
        font-size: 2rem;
      }
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .feature-card {
      padding: 30px 24px;
    }
  }
}
</style>
