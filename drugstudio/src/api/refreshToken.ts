// import request from '/@/utils/request'

// export const expireToken = () => {
//   return request({
//     url: '/expireToken',
//     method: 'get',
//   })
// }

// export const refreshToken = () => {
//   return request({
//     url: '/refreshToken',
//     method: 'get',
//   })
// }


import { AUTH_API } from '/@/config/api.config'
import request from '/@/utils/request'
import { useUserStore } from '/@/store/modules/user'

export const expireToken = () => {
  return request({
    url: '/expireToken',
    method: 'get',
  })
}

export const refreshToken = () => {
  const userStore = useUserStore()
  const refreshTokenValue = userStore.getRefreshToken
  
  return request({
    url: AUTH_API.REFRESH_TOKEN,
    method: 'post',
    data: { refresh_token: refreshTokenValue },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
