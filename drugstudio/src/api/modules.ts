import request from '/@/utils/request'
import { API_BASE_URL } from '/@/config/api.config'
import { ModuleCategory } from '/@/types/moduleTypes'

// 通用模块API
export const MODULES_API = {
    GET_CATEGORIES: `${API_BASE_URL}/api/v1/modules/categories`,
    GET_MODULES: `${API_BASE_URL}/api/v1/modules`,
    GET_MODULE: (id: string) => `${API_BASE_URL}/api/v1/modules/${id}`,
}

// 获取所有类目信息
export const getCategories = () => {
    return request({
        url: MODULES_API.GET_CATEGORIES,
        method: 'get',
    })
}

// 获取模块列表（支持按类目筛选）
export const getModules = (category?: ModuleCategory) => {
    return request({
        url: MODULES_API.GET_MODULES,
        method: 'get',
        params: category ? { category } : {},
    })
}

// 获取单个模块详情
export const getModule = (id: string) => {
    return request({
        url: MODULES_API.GET_MODULE(id),
        method: 'get',
    })
}

// 为了向后兼容，保留原有的分子生成API函数
export const getMoleculeGenerationModules = () => {
    return getModules(ModuleCategory.MOLECULE_GENERATION)
}

export const getMoleculeGenerationModule = (id: string) => {
    return getModule(id)
} 