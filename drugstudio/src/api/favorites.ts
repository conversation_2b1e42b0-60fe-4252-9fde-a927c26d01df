import request from '/@/utils/request'
import { API_BASE_URL } from '/@/config/api.config'

// 收藏相关API
export const FAVORITES_API = {
    TOGGLE_FAVORITE: `${API_BASE_URL}/api/v1/favorites/toggle`,
    GET_STATUS: (moduleId: string) => `${API_BASE_URL}/api/v1/favorites/status/${moduleId}`,
    GET_MY_FAVORITES: `${API_BASE_URL}/api/v1/favorites/my-favorites`,
    REMOVE_FAVORITE: (moduleId: string) => `${API_BASE_URL}/api/v1/favorites/${moduleId}`,
}

// 切换收藏状态
export const toggleFavorite = (moduleId: string) => {
    return request({
        url: FAVORITES_API.TOGGLE_FAVORITE,
        method: 'post',
        data: { module_id: moduleId },
    })
}

// 获取收藏状态
export const getFavoriteStatus = (moduleId: string) => {
    return request({
        url: FAVORITES_API.GET_STATUS(moduleId),
        method: 'get',
    })
}

// 获取我的收藏列表
export const getMyFavorites = (page = 1, size = 20) => {
    return request({
        url: FAVORITES_API.GET_MY_FAVORITES,
        method: 'get',
        params: { page, size },
    })
}

// 取消收藏
export const removeFavorite = (moduleId: string) => {
    return request({
        url: FAVORITES_API.REMOVE_FAVORITE(moduleId),
        method: 'delete',
    })
} 