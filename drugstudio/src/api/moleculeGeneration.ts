import request from '/@/utils/request'
import { API_BASE_URL } from '/@/config/api.config'

// 分子生成模块API
export const MOLECULE_GENERATION_API = {
    GET_MODULES: `${API_BASE_URL}/api/v1/molecule-generation`,
    GET_MODULE: (id: string) => `${API_BASE_URL}/api/v1/molecule-generation/${id}`,
}

// 获取分子生成模块列表
export const getMoleculeGenerationModules = () => {
    return request({
        url: MOLECULE_GENERATION_API.GET_MODULES,
        method: 'get',
    })
}

// 获取单个分子生成模块详情
export const getMoleculeGenerationModule = (id: string) => {
    return request({
        url: MOLECULE_GENERATION_API.GET_MODULE(id),
        method: 'get',
    })
} 