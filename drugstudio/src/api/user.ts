// import { loginRSA } from '/@/config'
// import { encryptedData } from '/@/utils/encrypt'
// import request from '/@/utils/request'

// interface FormType {
//   password: string
//   password2?: string
//   phone: string
//   phoneCode: string
//   username: string
//   verificationCode: string
// }

// export const login = async (data: any) => {
//   if (loginRSA) data = { ...data, password: await encryptedData(data) }
//   return request({
//     url: '/login',
//     method: 'post',
//     data,
//   })
// }

// export const getUserInfo = () => {
//   return request({
//     url: '/userInfo',
//     method: 'get',
//   })
// }

// export const logout = () => {
//   return request({
//     url: '/logout',
//     method: 'get',
//   })
// }

// export const register = (data: FormType) => {
//   return request({
//     url: '/register',
//     method: 'post',
//     data,
//   })
// }

// export const password = (data: FormType) => {
//   return request({
//     url: '/password',
//     method: 'post',
//     data,
//   })
// }

// export const lock = () => {
//   return request({
//     url: '/lock',
//     method: 'get',
//   })
// }


import { loginRSA } from '/@/config'
import { AUTH_API } from '/@/config/api.config'
import { encryptedData } from '/@/utils/encrypt'
import request from '/@/utils/request'

interface FormType {
  password: string
  password2?: string
  phone?: string // Phone is now optional
  email?: string // Added email field
  username: string
  verificationCode?: string
}

export const login = async (data: any) => {
  let loginData = { ...data };
  if (loginRSA) {
    loginData.password = await encryptedData(data.password); // Only encrypt the password
  }
  return request({
    url: AUTH_API.LOGIN,
    method: 'post',
    data: loginData,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export const getUserInfo = () => {
  return request({
    url: AUTH_API.USER_INFO,
    method: 'get',
  })
}

export const logout = () => {
  return request({
    url: AUTH_API.LOGOUT,
    method: 'get',
  })
}

export const register = (data: FormType) => {
  return request({
    url: AUTH_API.REGISTER,
    method: 'post',
    data,
  })
}

export const password = (data: FormType) => {
  return request({
    url: AUTH_API.RESET_PASSWORD,
    method: 'post',
    data,
  })
}

export const lock = () => {
  return request({
    url: '/lock',
    method: 'get',
  })
}
