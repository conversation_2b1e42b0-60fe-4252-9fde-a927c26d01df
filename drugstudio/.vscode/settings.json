{"[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "docthis.authorName": "chuzhixin <EMAIL>", "docthis.enableHungarianNotationEvaluation": true, "docthis.includeAuthorTag": true, "docthis.includeDescriptionTag": true, "docthis.inferTypesFromNames": true, "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.quickSuggestions": {"strings": true}, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.tabSize": 2, "emmet.triggerExpansionOnTab": true, "explorer.confirmDelete": false, "files.autoSave": "onFocusChange", "files.eol": "\n", "files.exclude": {"**/.idea": true}, "git.autofetch": true, "git.confirmSync": false, "git.enableSmartCommit": true, "javascript.format.enable": true, "javascript.format.semicolons": "remove", "javascript.updateImportsOnFileMove.enabled": "always", "liveServer.settings.donotShowInfoMsg": true, "path-intellisense.mappings": {"@": "${workspaceRoot}/src"}, "prettier.htmlWhitespaceSensitivity": "ignore", "prettier.vueIndentScriptAndStyle": true, "stylelint.validate": ["vue", "scss"], "typescript.format.semicolons": "remove", "typescript.updateImportsOnFileMove.enabled": "always", "vue.codeActions.savingTimeLimit": 100000, "workbench.colorTheme": "One Monokai", "cSpell.words": ["actived", "<PERSON><PERSON><PERSON>", "appinstalled", "autofix", "autoresize", "axios", "backtop", "beforeinstallprompt", "<PERSON>ian", "bilibili", "Boxplot", "brotli", "btns", "cascader", "catched", "chuz<PERSON><PERSON>", "cnpm", "codepen", "commafy", "cropdata", "cropend", "croppreview", "cropvisible", "ctitle", "curveness", "daterange", "datetime", "datetimerange", "deepseek", "douban", "do<PERSON>o", "echarts", "gantt", "gitee", "globle", "headerbtn", "<PERSON><PERSON><PERSON>", "jsencrypt", "kangc", "lllustration", "lllustrations", "logicflow", "Mbps", "messagebox", "mockjs", "MSIE", "nocheck", "nprogress", "officedocument", "onwarn", "openai", "Openeds", "opentiny", "openxmlformats", "picocolors", "pinia", "popconfirm", "<PERSON><PERSON><PERSON>", "RBAC", "remixicon", "<PERSON><PERSON>", "sortablejs", "spreadsheetml", "stylelint", "sundan", "<PERSON><PERSON><PERSON>", "textareas", "tiktok", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "treemap", "typeit", "unplugin", "unref", "unsub", "vcode", "vite", "vitebar", "v<PERSON><PERSON><PERSON>", "vueuse", "wangeditor", "wechat", "weibo", "x<PERSON><PERSON>", "xgplayer", "<PERSON><PERSON><PERSON><PERSON>", "yiyan", "<PERSON><PERSON>wenjia", "zlevel", "zoomer", "zxwk"]}