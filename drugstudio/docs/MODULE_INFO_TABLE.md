# 模块信息表格功能

## 概述

本功能实现了一个通用的模块信息表格组件，用于展示模块的详细信息，包括编号、UUID、名称、封面、描述、特性、标签、作者、发布时间以及参考文献等字段。

## 功能特性

### 表格字段

| 字段名 | 描述 | 类型 |
|--------|------|------|
| 编号 | 模块ID | string |
| UUID | 模块唯一标识符 | string |
| 名称 | 模块名称 | string |
| 封面 | 模块封面图片 | image |
| 描述 | 模块详细描述 | string |
| 特性 | 模块特性列表 | array |
| 标签 | 模块标签 | array |
| 作者 | 模块作者 | string |
| 发布时间 | 模块发布时间 | datetime |
| 参考文献 | 相关参考文献 | array |

### 组件结构

```
src/
├── components/
│   ├── ModuleInfoTable.vue     # 模块信息表格组件
│   └── ModuleInfoTab.vue       # 通用模块信息Tab组件
├── views/sysmodules/modules/
│   ├── 001/InfoTab.vue         # Moses模块信息Tab
│   ├── 002/InfoTab.vue         # 蛋白质设计模块信息Tab
│   └── 003/InfoTab.vue         # 分子对接模块信息Tab
└── types/moduleTypes.ts        # 模块类型定义
```

## 使用方法

### 1. 使用ModuleInfoTable组件

```vue
<template>
  <ModuleInfoTable :module="moduleData" />
</template>

<script setup>
import ModuleInfoTable from '/@/components/ModuleInfoTable.vue'
import type { Module } from '/@/types/moduleTypes'

const moduleData: Module = {
  id: '001',
  uuid: '550e8400-e29b-41d4-a716-************',
  name: 'De novo Generation (Moses)',
  // ... 其他字段
}
</script>
```

### 2. 使用ModuleInfoTab组件

```vue
<template>
  <ModuleInfoTab
    :module="module"
    :default-features="defaultFeatures"
    :default-references="defaultReferences"
    :default-author="defaultAuthor"
    :default-cover-image="defaultCoverImage"
  />
</template>

<script setup>
import ModuleInfoTab from '/@/components/ModuleInfoTab.vue'

const defaultFeatures = [
  '特性1',
  '特性2',
  '特性3'
]

const defaultReferences = [
  {
    title: '论文标题',
    authors: ['作者1', '作者2'],
    journal: '期刊名称',
    year: 2023,
    doi: '10.1000/example'
  }
]
</script>
```

## 数据结构

### Module接口

```typescript
export interface Module {
  id: string
  uuid: string
  name: string
  shortDescription: string
  description: string
  markdownContent: string
  
  // 展示信息
  coverImage?: string
  features: string[]
  author?: string
  publishDate?: string
  references: Reference[]
  
  icon: string
  tags: string[]
  category: ModuleCategory
  parameters: ModuleParameter[]
  models: ModuleModel[]
  moleculeCount: string
  seed: string
  results: ModuleResult[]
  is_favorited?: boolean
}
```

### Reference接口

```typescript
export interface Reference {
  title: string
  authors: string[]
  journal?: string
  year?: number
  doi?: string
  url?: string
}
```

## 后端支持

### 数据库字段

后端Module模型已添加以下新字段：

- `uuid`: UUID类型，模块唯一标识符
- `cover_image`: 封面图片URL
- `features`: JSON数组，特性列表
- `author`: 作者信息
- `publish_date`: 发布时间
- `references`: JSON数组，参考文献列表

### API响应

API响应已更新以包含新字段：

```json
{
  "id": "001",
  "uuid": "550e8400-e29b-41d4-a716-************",
  "name": "De novo Generation (Moses)",
  "coverImage": "/images/modules/moses-cover.jpg",
  "features": [
    "支持多种生成模型：VAE、AAE、ORGAN、REINVENT",
    "可控的分子生成参数"
  ],
  "author": "DrugStudio Team",
  "publishDate": "2023-12-01T10:00:00Z",
  "references": [
    {
      "title": "论文标题",
      "authors": ["作者1", "作者2"],
      "journal": "期刊名称",
      "year": 2020,
      "doi": "10.1000/example"
    }
  ]
}
```

## 测试

可以通过访问测试页面来验证功能：

```
/test/module-info
```

该页面展示了三个不同模块的信息表格示例。

## 扩展性

### 添加新模块

1. 在 `src/views/sysmodules/modules/` 下创建新的模块目录
2. 创建 `InfoTab.vue` 文件
3. 使用 `ModuleInfoTab` 组件并提供默认数据
4. 在模块配置中注册新模块

### 自定义字段

如需添加新的表格字段：

1. 更新 `Module` 接口定义
2. 修改 `ModuleInfoTable.vue` 组件
3. 更新后端数据库模型和API响应
4. 添加相应的数据迁移脚本

## 注意事项

1. 确保所有模块都提供了必要的默认数据
2. 图片资源需要放置在正确的路径下
3. 参考文献的DOI链接会自动生成
4. UUID字段支持复制功能（点击可复制完整UUID）
